This is pdfTeX, Version 3.14159265-2.6-1.40.20 (TeX Live 2019) (preloaded format=pdflatex 2020.4.5)  9 APR 2020 10:12
entering extended mode
 restricted \write18 enabled.
 file:line:error style messages enabled.
 %&-line parsing enabled.
**/home/<USER>/Documents/org.functionary/elixir/wraft-docs-api/lib/slugs/pletter/template.tex
(/home/<USER>/Documents/org.functionary/elixir/wraft-docs-api/lib/slugs/pletter/template.tex
LaTeX2e <2018-12-01>

/home/<USER>/Documents/org.functionary/elixir/wraft-docs-api/lib/slugs/pletter/template.tex:3: LaTeX Error: Missing \begin{document}.

See the LaTeX manual or LaTeX Companion for explanation.
Type  H <return>  for immediate help.
 ...

l.3 $
     if(colorlinks)$
You're in trouble here.  Try typing  <return>  to proceed.
If that doesn't work, type  X <return>  to quit.

LaTeX Font Info:    External font `cmex10' loaded for size
(Font)              <7> on input line 3.
LaTeX Font Info:    External font `cmex10' loaded for size
(Font)              <5> on input line 3.
(/usr/local/texlive/2019/texmf-dist/tex/latex/base/letter.cls
Document Class: letter 2014/09/29 v1.2z Standard LaTeX document class
(/usr/local/texlive/2019/texmf-dist/tex/latex/base/size10.clo
File: size10.clo 2018/09/03 v1.4i Standard LaTeX file (size option)
)
\longindentation=\dimen102
\indentedwidth=\dimen103
\labelcount=\count80

Overfull \hbox (82.02354pt too wide) in paragraph at lines 3--405
[]$\OML/cmm/m/it/10 if\OT1/cmr/m/n/10 (\OML/cmm/m/it/10 colorlinks\OT1/cmr/m/n/10 )$
 []


Overfull \hbox (25.28087pt too wide) in paragraph at lines 3--405
\OML/cmm/m/it/10 endif$
 []

[1{/usr/local/texlive/2019/texmf-var/fonts/map/pdftex/updmap/pdftex.map}])

/home/<USER>/Documents/org.functionary/elixir/wraft-docs-api/lib/slugs/pletter/template.tex:7: LaTeX Error: Missing \begin{document}.

See the LaTeX manual or LaTeX Companion for explanation.
Type  H <return>  for immediate help.
 ...

l.7 $
     if(fontfamily)$
You're in trouble here.  Try typing  <return>  to proceed.
If that doesn't work, type  X <return>  to quit.


! LaTeX Error: File `$fontfamily$.sty' not found.

Type X to quit or <RETURN> to proceed,
or enter new name. (Default extension: sty)

Enter file name:
/home/<USER>/Documents/org.functionary/elixir/wraft-docs-api/lib/slugs/pletter/template.tex:9: Emergency stop.
<read *>

l.9 $
     else$^^M
*** (cannot \read from terminal in nonstop modes)


Here is how much of TeX's memory you used:
 148 strings out of 492623
 2065 string characters out of 6129538
 61434 words of memory out of 5000000
 4148 multiletter control sequences out of 15000+600000
 3640 words of font info for 14 fonts, out of 8000000 for 9000
 1141 hyphenation exceptions out of 8191
 20i,4n,22p,265b,113s stack positions out of 5000i,500n,10000p,200000b,80000s
/home/<USER>/Documents/org.functionary/elixir/wraft-docs-api/lib/slugs/pletter/template.tex:9:  ==> Fatal error occurred, no output PDF file produced!
