\relax
\providecommand\hyper@newdestlabel[2]{}
\providecommand\HyperFirstAtBeginDocument{\AtBeginDocument}
\HyperFirstAtBeginDocument{\ifx\hyper@anchor\@undefined
\global\let\oldcontentsline\contentsline
\gdef\contentsline#1#2#3#4{\oldcontentsline{#1}{#2}{#3}}
\global\let\oldnewlabel\newlabel
\gdef\newlabel#1#2{\newlabelxx{#1}#2}
\gdef\newlabelxx#1#2#3#4#5#6{\oldnewlabel{#1}{{#2}{#3}}}
\AtEndDocument{\ifx\hyper@anchor\@undefined
\let\contentsline\oldcontentsline
\let\newlabel\oldnewlabel
\fi}
\fi}
\global\let\hyper@last\relax
\gdef\HyperFirstAtBeginDocument#1{#1}
\providecommand*\HyPL@Entry[1]{}
\HyPL@Entry{0<</S/D>>}
\select@language {english}
\@writefile{toc}{\select@language {english}}
\@writefile{lof}{\select@language {english}}
\@writefile{lot}{\select@language {english}}
\select@language {english}
\@writefile{toc}{\select@language {english}}
\@writefile{lof}{\select@language {english}}
\@writefile{lot}{\select@language {english}}
\HyPL@Entry{1<</S/D>>}
\HyPL@Entry{2<</S/D>>}
