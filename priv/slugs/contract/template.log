This is XeTeX, Version 3.14159265-2.6-0.999991 (TeX Live 2019) (preloaded format=xelatex 2019.12.20)  8 APR 2020 14:30
entering extended mode
 restricted \write18 enabled.
 file:line:error style messages enabled.
 %&-line parsing enabled.
**/Users/<USER>/Sites/Elixir/wraft-docs-api/lib/slugs/contract/template
(/Users/<USER>/Sites/Elixir/wraft-docs-api/lib/slugs/contract/template.tex
LaTeX2e <2018-12-01>
(/usr/local/texlive/2019/texmf-dist/tex/latex/base/article.cls
Document Class: article 2018/09/03 v1.4i Standard LaTeX document class
(/usr/local/texlive/2019/texmf-dist/tex/latex/base/size11.clo
File: size11.clo 2018/09/03 v1.4i Standard LaTeX file (size option)
)
\c@part=\count80
\c@section=\count81
\c@subsection=\count82
\c@subsubsection=\count83
\c@paragraph=\count84
\c@subparagraph=\count85
\c@figure=\count86
\c@table=\count87
\abovecaptionskip=\skip41
\belowcaptionskip=\skip42
\bibindent=\dimen102
) (/usr/local/texlive/2019/texmf-dist/tex/latex/base/inputenc.sty
Package: inputenc 2018/08/11 v1.3c Input encoding file
\inpenc@prehook=\toks14
\inpenc@posthook=\toks15


Package inputenc Warning: inputenc package ignored with utf8 based engines.

) (/usr/local/texlive/2019/texmf-dist/tex/generic/babel/babel.sty
Package: babel 2019/05/04 3.31 The Babel package
 (/usr/local/texlive/2019/texmf-dist/tex/generic/babel/switch.def
File: switch.def 2019/05/04 3.31 Babel switching mechanism
) (/usr/local/texlive/2019/texmf-dist/tex/generic/babel-english/english.ldf
Language: english 2017/06/06 v3.3r English support from the babel system
 (/usr/local/texlive/2019/texmf-dist/tex/generic/babel/babel.def
File: babel.def 2019/05/04 3.31 Babel common definitions
\babel@savecnt=\count88
\U@D=\dimen103
 (/usr/local/texlive/2019/texmf-dist/tex/generic/babel/xebabel.def (/usr/local/texlive/2019/texmf-dist/tex/generic/babel/txtbabel.def))
\bbl@dirlevel=\count89
)
\l@canadian = a dialect from \language\l@american
\l@australian = a dialect from \language\l@british
\l@newzealand = a dialect from \language\l@british
)) (/usr/local/texlive/2019/texmf-dist/tex/latex/fontspec/fontspec.sty (/usr/local/texlive/2019/texmf-dist/tex/latex/l3packages/xparse/xparse.sty (/usr/local/texlive/2019/texmf-dist/tex/latex/l3kernel/expl3.sty
Package: expl3 2019-05-07 L3 programming layer (loader)
 (/usr/local/texlive/2019/texmf-dist/tex/latex/l3kernel/expl3-code.tex
Package: expl3 2019-05-07 L3 programming layer (code)
\c_max_int=\count90
\l_tmpa_int=\count91
\l_tmpb_int=\count92
\g_tmpa_int=\count93
\g_tmpb_int=\count94
\g__kernel_prg_map_int=\count95
\c__ior_term_ior=\count96
\c_log_iow=\count97
\l_iow_line_count_int=\count98
\l__iow_line_target_int=\count99
\l__iow_one_indent_int=\count100
\l__iow_indent_int=\count101
\c_zero_dim=\dimen104
\c_max_dim=\dimen105
\l_tmpa_dim=\dimen106
\l_tmpb_dim=\dimen107
\g_tmpa_dim=\dimen108
\g_tmpb_dim=\dimen109
\c_zero_skip=\skip43
\c_max_skip=\skip44
\l_tmpa_skip=\skip45
\l_tmpb_skip=\skip46
\g_tmpa_skip=\skip47
\g_tmpb_skip=\skip48
\c_zero_muskip=\muskip10
\c_max_muskip=\muskip11
\l_tmpa_muskip=\muskip12
\l_tmpb_muskip=\muskip13
\g_tmpa_muskip=\muskip14
\g_tmpb_muskip=\muskip15
\l_keys_choice_int=\count102
\l__intarray_loop_int=\count103
\c__intarray_sp_dim=\dimen110
\g__intarray_font_int=\count104
\c__fp_leading_shift_int=\count105
\c__fp_middle_shift_int=\count106
\c__fp_trailing_shift_int=\count107
\c__fp_big_leading_shift_int=\count108
\c__fp_big_middle_shift_int=\count109
\c__fp_big_trailing_shift_int=\count110
\c__fp_Bigg_leading_shift_int=\count111
\c__fp_Bigg_middle_shift_int=\count112
\c__fp_Bigg_trailing_shift_int=\count113
\g__fp_array_int=\count114
\l__fp_array_loop_int=\count115
\l__sort_length_int=\count116
\l__sort_min_int=\count117
\l__sort_top_int=\count118
\l__sort_max_int=\count119
\l__sort_true_max_int=\count120
\l__sort_block_int=\count121
\l__sort_begin_int=\count122
\l__sort_end_int=\count123
\l__sort_A_int=\count124
\l__sort_B_int=\count125
\l__sort_C_int=\count126
\l__tl_analysis_normal_int=\count127
\l__tl_analysis_index_int=\count128
\l__tl_analysis_nesting_int=\count129
\l__tl_analysis_type_int=\count130
\l__regex_internal_a_int=\count131
\l__regex_internal_b_int=\count132
\l__regex_internal_c_int=\count133
\l__regex_balance_int=\count134
\l__regex_group_level_int=\count135
\l__regex_mode_int=\count136
\c__regex_cs_in_class_mode_int=\count137
\c__regex_cs_mode_int=\count138
\l__regex_catcodes_int=\count139
\l__regex_default_catcodes_int=\count140
\c__regex_catcode_L_int=\count141
\c__regex_catcode_O_int=\count142
\c__regex_catcode_A_int=\count143
\c__regex_all_catcodes_int=\count144
\l__regex_show_lines_int=\count145
\l__regex_min_state_int=\count146
\l__regex_max_state_int=\count147
\l__regex_left_state_int=\count148
\l__regex_right_state_int=\count149
\l__regex_capturing_group_int=\count150
\l__regex_min_pos_int=\count151
\l__regex_max_pos_int=\count152
\l__regex_curr_pos_int=\count153
\l__regex_start_pos_int=\count154
\l__regex_success_pos_int=\count155
\l__regex_curr_char_int=\count156
\l__regex_curr_catcode_int=\count157
\l__regex_last_char_int=\count158
\l__regex_case_changed_char_int=\count159
\l__regex_curr_state_int=\count160
\l__regex_step_int=\count161
\l__regex_min_active_int=\count162
\l__regex_max_active_int=\count163
\l__regex_replacement_csnames_int=\count164
\l__regex_match_count_int=\count165
\l__regex_min_submatch_int=\count166
\l__regex_submatch_int=\count167
\l__regex_zeroth_submatch_int=\count168
\g__regex_trace_regex_int=\count169
\c_empty_box=\box27
\l_tmpa_box=\box28
\l_tmpb_box=\box29
\g_tmpa_box=\box30
\g_tmpb_box=\box31
\l__box_top_dim=\dimen111
\l__box_bottom_dim=\dimen112
\l__box_left_dim=\dimen113
\l__box_right_dim=\dimen114
\l__box_top_new_dim=\dimen115
\l__box_bottom_new_dim=\dimen116
\l__box_left_new_dim=\dimen117
\l__box_right_new_dim=\dimen118
\l__box_internal_box=\box32
\l__coffin_internal_box=\box33
\l__coffin_internal_dim=\dimen119
\l__coffin_offset_x_dim=\dimen120
\l__coffin_offset_y_dim=\dimen121
\l__coffin_x_dim=\dimen122
\l__coffin_y_dim=\dimen123
\l__coffin_x_prime_dim=\dimen124
\l__coffin_y_prime_dim=\dimen125
\c_empty_coffin=\box34
\l__coffin_aligned_coffin=\box35
\l__coffin_aligned_internal_coffin=\box36
\l_tmpa_coffin=\box37
\l_tmpb_coffin=\box38
\g_tmpa_coffin=\box39
\g_tmpb_coffin=\box40
\l__coffin_bounding_shift_dim=\dimen126
\l__coffin_left_corner_dim=\dimen127
\l__coffin_right_corner_dim=\dimen128
\l__coffin_bottom_corner_dim=\dimen129
\l__coffin_top_corner_dim=\dimen130
\l__coffin_scaled_total_height_dim=\dimen131
\l__coffin_scaled_width_dim=\dimen132
\c__coffin_empty_coffin=\box41
\l__coffin_display_coffin=\box42
\l__coffin_display_coord_coffin=\box43
\l__coffin_display_pole_coffin=\box44
\l__coffin_display_offset_dim=\dimen133
\l__coffin_display_x_dim=\dimen134
\l__coffin_display_y_dim=\dimen135
\g__file_internal_ior=\read1
\l__seq_internal_a_int=\count170
\l__seq_internal_b_int=\count171
\c__deprecation_minus_one=\count172
) (/usr/local/texlive/2019/texmf-dist/tex/latex/l3kernel/l3xdvipdfmx.def
File: l3xdvipdfmx.def 2019-04-06 v L3 Experimental driver: xdvipdfmx
\g__driver_image_int=\count173
\l__driver_pdf_tmp_box=\box45
\g__driver_pdf_object_int=\count174
\g__driver_pdf_annotation_int=\count175
))
Package: xparse 2019-05-03 L3 Experimental document command parser
\l__xparse_current_arg_int=\count176
\g__xparse_grabber_int=\count177
\l__xparse_m_args_int=\count178
\l__xparse_v_nesting_int=\count179
)
Package: fontspec 2019/03/15 v2.7c Font selection for XeLaTeX and LuaLaTeX
 (/usr/local/texlive/2019/texmf-dist/tex/latex/fontspec/fontspec-xetex.sty
Package: fontspec-xetex 2019/03/15 v2.7c Font selection for XeLaTeX and LuaLaTeX
\l__fontspec_script_int=\count180
\l__fontspec_language_int=\count181
\l__fontspec_strnum_int=\count182
\l__fontspec_tmp_int=\count183
\l__fontspec_tmpa_int=\count184
\l__fontspec_tmpb_int=\count185
\l__fontspec_tmpc_int=\count186
\l__fontspec_em_int=\count187
\l__fontspec_emdef_int=\count188
\l__fontspec_strong_int=\count189
\l__fontspec_strongdef_int=\count190
\l__fontspec_tmpa_dim=\dimen136
\l__fontspec_tmpb_dim=\dimen137
\l__fontspec_tmpc_dim=\dimen138
 (/usr/local/texlive/2019/texmf-dist/tex/latex/base/fontenc.sty
Package: fontenc 2018/08/11 v2.0j Standard LaTeX package
 (/usr/local/texlive/2019/texmf-dist/tex/latex/base/tuenc.def
File: tuenc.def 2018/08/11 v2.0j Standard LaTeX file
LaTeX Font Info:    Redeclaring font encoding TU on input line 82.
)) (/usr/local/texlive/2019/texmf-dist/tex/latex/fontspec/fontspec.cfg)
LaTeX Info: Redefining \itshape on input line 4051.
LaTeX Info: Redefining \slshape on input line 4056.
LaTeX Info: Redefining \scshape on input line 4061.
LaTeX Info: Redefining \upshape on input line 4066.
LaTeX Info: Redefining \em on input line 4096.
LaTeX Info: Redefining \emph on input line 4121.
)) (/usr/local/texlive/2019/texmf-dist/tex/latex/eurosym/eurosym.sty
Package: eurosym 1998/08/06 v1.1 European currency symbol ``Euro''
\@eurobox=\box46
) (/usr/local/texlive/2019/texmf-dist/tex/latex/amsfonts/amssymb.sty
Package: amssymb 2013/01/14 v3.01 AMS font symbols
 (/usr/local/texlive/2019/texmf-dist/tex/latex/amsfonts/amsfonts.sty
Package: amsfonts 2013/01/14 v3.01 Basic AMSFonts support
\@emptytoks=\toks16
\symAMSa=\mathgroup4
\symAMSb=\mathgroup5
LaTeX Font Info:    Overwriting math alphabet `\mathfrak' in version `bold'
(Font)                  U/euf/m/n --> U/euf/b/n on input line 106.
)) (/usr/local/texlive/2019/texmf-dist/tex/latex/mathtools/mathtools.sty
Package: mathtools 2018/01/08 v1.21 mathematical typesetting tools
 (/usr/local/texlive/2019/texmf-dist/tex/latex/graphics/keyval.sty
Package: keyval 2014/10/28 v1.15 key=value parser (DPC)
\KV@toks@=\toks17
) (/usr/local/texlive/2019/texmf-dist/tex/latex/tools/calc.sty
Package: calc 2017/05/25 v4.3 Infix arithmetic (KKT,FJ)
\calc@Acount=\count191
\calc@Bcount=\count192
\calc@Adimen=\dimen139
\calc@Bdimen=\dimen140
\calc@Askip=\skip49
\calc@Bskip=\skip50
LaTeX Info: Redefining \setlength on input line 80.
LaTeX Info: Redefining \addtolength on input line 81.
\calc@Ccount=\count193
\calc@Cskip=\skip51
) (/usr/local/texlive/2019/texmf-dist/tex/latex/mathtools/mhsetup.sty
Package: mhsetup 2017/03/31 v1.3 programming setup (MH)
) (/usr/local/texlive/2019/texmf-dist/tex/latex/amsmath/amsmath.sty
Package: amsmath 2018/12/01 v2.17b AMS math features
\@mathmargin=\skip52

For additional information on amsmath, use the `?' option.
(/usr/local/texlive/2019/texmf-dist/tex/latex/amsmath/amstext.sty
Package: amstext 2000/06/29 v2.01 AMS text
 (/usr/local/texlive/2019/texmf-dist/tex/latex/amsmath/amsgen.sty
File: amsgen.sty 1999/11/30 v2.0 generic functions
\@emptytoks=\toks18
\ex@=\dimen141
)) (/usr/local/texlive/2019/texmf-dist/tex/latex/amsmath/amsbsy.sty
Package: amsbsy 1999/11/29 v1.2d Bold Symbols
\pmbraise@=\dimen142
) (/usr/local/texlive/2019/texmf-dist/tex/latex/amsmath/amsopn.sty
Package: amsopn 2016/03/08 v2.02 operator names
)
\inf@bad=\count194
LaTeX Info: Redefining \frac on input line 223.
\uproot@=\count195
\leftroot@=\count196
LaTeX Info: Redefining \overline on input line 385.
\classnum@=\count197
\DOTSCASE@=\count198
LaTeX Info: Redefining \ldots on input line 482.
LaTeX Info: Redefining \dots on input line 485.
LaTeX Info: Redefining \cdots on input line 606.
\Mathstrutbox@=\box47
\strutbox@=\box48
\big@size=\dimen143
LaTeX Font Info:    Redeclaring font encoding OML on input line 729.
LaTeX Font Info:    Redeclaring font encoding OMS on input line 730.
\macc@depth=\count199
\c@MaxMatrixCols=\count266
\dotsspace@=\muskip16
\c@parentequation=\count267
\dspbrk@lvl=\count268
\tag@help=\toks19
\row@=\count269
\column@=\count270
\maxfields@=\count271
\andhelp@=\toks20
\eqnshift@=\dimen144
\alignsep@=\dimen145
\tagshift@=\dimen146
\tagwidth@=\dimen147
\totwidth@=\dimen148
\lineht@=\dimen149
\@envbody=\toks21
\multlinegap=\skip53
\multlinetaggap=\skip54
\mathdisplay@stack=\toks22
LaTeX Info: Redefining \[ on input line 2844.
LaTeX Info: Redefining \] on input line 2845.
)
LaTeX Info: Thecontrolsequence`\('isalreadyrobust on input line 129.
LaTeX Info: Thecontrolsequence`\)'isalreadyrobust on input line 129.
LaTeX Info: Thecontrolsequence`\['isalreadyrobust on input line 129.
LaTeX Info: Thecontrolsequence`\]'isalreadyrobust on input line 129.
\g_MT_multlinerow_int=\count272
\l_MT_multwidth_dim=\dimen150
\origjot=\skip55
\l_MT_shortvdotswithinadjustabove_dim=\dimen151
\l_MT_shortvdotswithinadjustbelow_dim=\dimen152
\l_MT_above_intertext_sep=\dimen153
\l_MT_below_intertext_sep=\dimen154
\l_MT_above_shortintertext_sep=\dimen155
\l_MT_below_shortintertext_sep=\dimen156
) (/usr/local/texlive/2019/texmf-dist/tex/latex/upquote/upquote.sty
Package: upquote 2012/04/19 v1.3 upright-quote and grave-accent glyphs in verbatim
 (/usr/local/texlive/2019/texmf-dist/tex/latex/base/textcomp.sty
Package: textcomp 2018/08/11 v2.0j Standard LaTeX package
Package textcomp Info: Sub-encoding information:
(textcomp)               5 = only ISO-Adobe without \textcurrency
(textcomp)               4 = 5 + \texteuro
(textcomp)               3 = 4 + \textohm
(textcomp)               2 = 3 + \textestimated + \textcurrency
(textcomp)               1 = TS1 - \textcircled - \t
(textcomp)               0 = TS1 (full)
(textcomp)             Font families with sub-encoding setting implement
(textcomp)             only a restricted character set as indicated.
(textcomp)             Family '?' is the default used for unknown fonts.
(textcomp)             See the documentation for details.
Package textcomp Info: Setting ? sub-encoding to TS1/1 on input line 79.
 (/usr/local/texlive/2019/texmf-dist/tex/latex/base/ts1enc.def
File: ts1enc.def 2001/06/05 v3.0e (jk/car/fm) Standard LaTeX file
)
LaTeX Info: Redefining \oldstylenums on input line 334.
Package textcomp Info: Setting cmr sub-encoding to TS1/0 on input line 349.
Package textcomp Info: Setting cmss sub-encoding to TS1/0 on input line 350.
Package textcomp Info: Setting cmtt sub-encoding to TS1/0 on input line 351.
Package textcomp Info: Setting cmvtt sub-encoding to TS1/0 on input line 352.
Package textcomp Info: Setting cmbr sub-encoding to TS1/0 on input line 353.
Package textcomp Info: Setting cmtl sub-encoding to TS1/0 on input line 354.
Package textcomp Info: Setting ccr sub-encoding to TS1/0 on input line 355.
Package textcomp Info: Setting ptm sub-encoding to TS1/4 on input line 356.
Package textcomp Info: Setting pcr sub-encoding to TS1/4 on input line 357.
Package textcomp Info: Setting phv sub-encoding to TS1/4 on input line 358.
Package textcomp Info: Setting ppl sub-encoding to TS1/3 on input line 359.
Package textcomp Info: Setting pag sub-encoding to TS1/4 on input line 360.
Package textcomp Info: Setting pbk sub-encoding to TS1/4 on input line 361.
Package textcomp Info: Setting pnc sub-encoding to TS1/4 on input line 362.
Package textcomp Info: Setting pzc sub-encoding to TS1/4 on input line 363.
Package textcomp Info: Setting bch sub-encoding to TS1/4 on input line 364.
Package textcomp Info: Setting put sub-encoding to TS1/5 on input line 365.
Package textcomp Info: Setting uag sub-encoding to TS1/5 on input line 366.
Package textcomp Info: Setting ugq sub-encoding to TS1/5 on input line 367.
Package textcomp Info: Setting ul8 sub-encoding to TS1/4 on input line 368.
Package textcomp Info: Setting ul9 sub-encoding to TS1/4 on input line 369.
Package textcomp Info: Setting augie sub-encoding to TS1/5 on input line 370.
Package textcomp Info: Setting dayrom sub-encoding to TS1/3 on input line 371.
Package textcomp Info: Setting dayroms sub-encoding to TS1/3 on input line 372.
Package textcomp Info: Setting pxr sub-encoding to TS1/0 on input line 373.
Package textcomp Info: Setting pxss sub-encoding to TS1/0 on input line 374.
Package textcomp Info: Setting pxtt sub-encoding to TS1/0 on input line 375.
Package textcomp Info: Setting txr sub-encoding to TS1/0 on input line 376.
Package textcomp Info: Setting txss sub-encoding to TS1/0 on input line 377.
Package textcomp Info: Setting txtt sub-encoding to TS1/0 on input line 378.
Package textcomp Info: Setting lmr sub-encoding to TS1/0 on input line 379.
Package textcomp Info: Setting lmdh sub-encoding to TS1/0 on input line 380.
Package textcomp Info: Setting lmss sub-encoding to TS1/0 on input line 381.
Package textcomp Info: Setting lmssq sub-encoding to TS1/0 on input line 382.
Package textcomp Info: Setting lmvtt sub-encoding to TS1/0 on input line 383.
Package textcomp Info: Setting lmtt sub-encoding to TS1/0 on input line 384.
Package textcomp Info: Setting qhv sub-encoding to TS1/0 on input line 385.
Package textcomp Info: Setting qag sub-encoding to TS1/0 on input line 386.
Package textcomp Info: Setting qbk sub-encoding to TS1/0 on input line 387.
Package textcomp Info: Setting qcr sub-encoding to TS1/0 on input line 388.
Package textcomp Info: Setting qcs sub-encoding to TS1/0 on input line 389.
Package textcomp Info: Setting qpl sub-encoding to TS1/0 on input line 390.
Package textcomp Info: Setting qtm sub-encoding to TS1/0 on input line 391.
Package textcomp Info: Setting qzc sub-encoding to TS1/0 on input line 392.
Package textcomp Info: Setting qhvc sub-encoding to TS1/0 on input line 393.
Package textcomp Info: Setting futs sub-encoding to TS1/4 on input line 394.
Package textcomp Info: Setting futx sub-encoding to TS1/4 on input line 395.
Package textcomp Info: Setting futj sub-encoding to TS1/4 on input line 396.
Package textcomp Info: Setting hlh sub-encoding to TS1/3 on input line 397.
Package textcomp Info: Setting hls sub-encoding to TS1/3 on input line 398.
Package textcomp Info: Setting hlst sub-encoding to TS1/3 on input line 399.
Package textcomp Info: Setting hlct sub-encoding to TS1/5 on input line 400.
Package textcomp Info: Setting hlx sub-encoding to TS1/5 on input line 401.
Package textcomp Info: Setting hlce sub-encoding to TS1/5 on input line 402.
Package textcomp Info: Setting hlcn sub-encoding to TS1/5 on input line 403.
Package textcomp Info: Setting hlcw sub-encoding to TS1/5 on input line 404.
Package textcomp Info: Setting hlcf sub-encoding to TS1/5 on input line 405.
Package textcomp Info: Setting pplx sub-encoding to TS1/3 on input line 406.
Package textcomp Info: Setting pplj sub-encoding to TS1/3 on input line 407.
Package textcomp Info: Setting ptmx sub-encoding to TS1/4 on input line 408.
Package textcomp Info: Setting ptmj sub-encoding to TS1/4 on input line 409.
)) (/usr/local/texlive/2019/texmf-dist/tex/latex/microtype/microtype.sty
Package: microtype 2019/02/28 v2.7b Micro-typographical refinements (RS)
\MT@toks=\toks23
\MT@count=\count273
LaTeX Info: Redefining \textls on input line 790.
\MT@outer@kern=\dimen157
LaTeX Info: Redefining \textmicrotypecontext on input line 1336.
\MT@listname@count=\count274
 (/usr/local/texlive/2019/texmf-dist/tex/latex/microtype/microtype-xetex.def
File: microtype-xetex.def 2019/02/28 v2.7b Definitions specific to xetex (RS)
LaTeX Info: Redefining \lsstyle on input line 258.
)
Package microtype Info: Loading configuration file microtype.cfg.
 (/usr/local/texlive/2019/texmf-dist/tex/latex/microtype/microtype.cfg
File: microtype.cfg 2019/02/28 v2.7b microtype main configuration file (RS)
)) (/usr/local/texlive/2019/texmf-dist/tex/latex/polyglossia/polyglossia.sty
Package: polyglossia 2019/04/04 v1.44 Alternative to Babel for XeLaTeX and LuaLaTeX
 (/usr/local/texlive/2019/texmf-dist/tex/latex/etoolbox/etoolbox.sty
Package: etoolbox 2018/08/19 v2.5f e-TeX tools for LaTeX (JAW)
\etb@tempcnta=\count275
) (/usr/local/texlive/2019/texmf-dist/tex/latex/makecmds/makecmds.sty
Package: makecmds 2009/09/03 v1.0a extra command making commands
) (/usr/local/texlive/2019/texmf-dist/tex/latex/xkeyval/xkeyval.sty
Package: xkeyval 2014/12/03 v2.7a package option processing (HA)
 (/usr/local/texlive/2019/texmf-dist/tex/generic/xkeyval/xkeyval.tex (/usr/local/texlive/2019/texmf-dist/tex/generic/xkeyval/xkvutils.tex
\XKV@toks=\toks24
\XKV@tempa@toks=\toks25
)
\XKV@depth=\count276
File: xkeyval.tex 2014/12/03 v2.7a key=value parser (HA)
)) (/usr/local/texlive/2019/texmf-dist/tex/generic/oberdiek/ifluatex.sty
Package: ifluatex 2016/05/16 v1.4 Provides the ifluatex switch (HO)
Package ifluatex Info: LuaTeX not detected.
) (/usr/local/texlive/2019/texmf-dist/tex/generic/ifxetex/ifxetex.sty
Package: ifxetex 2010/09/12 v0.6 Provides ifxetex conditional
)
\xpg@normalclass=\XeTeXcharclass1
) (/usr/local/texlive/2019/texmf-dist/tex/latex/graphics/graphicx.sty
Package: graphicx 2017/06/01 v1.1a Enhanced LaTeX Graphics (DPC,SPQR)
 (/usr/local/texlive/2019/texmf-dist/tex/latex/graphics/graphics.sty
Package: graphics 2017/06/25 v1.2c Standard LaTeX Graphics (DPC,SPQR)
 (/usr/local/texlive/2019/texmf-dist/tex/latex/graphics/trig.sty
Package: trig 2016/01/03 v1.10 sin cos tan (DPC)
) (/usr/local/texlive/2019/texmf-dist/tex/latex/graphics-cfg/graphics.cfg
File: graphics.cfg 2016/06/04 v1.11 sample graphics configuration
)
Package graphics Info: Driver file: xetex.def on input line 99.
 (/usr/local/texlive/2019/texmf-dist/tex/latex/graphics-def/xetex.def
File: xetex.def 2017/06/24 v5.0h Graphics/color driver for xetex
))
\Gin@req@height=\dimen158
\Gin@req@width=\dimen159
) (/usr/local/texlive/2019/texmf-dist/tex/latex/oberdiek/grffile.sty
Package: grffile 2017/06/30 v1.18 Extended file name support for graphics (HO)
 (/usr/local/texlive/2019/texmf-dist/tex/generic/oberdiek/ifpdf.sty
Package: ifpdf 2018/09/07 v3.3 Provides the ifpdf switch
) (/usr/local/texlive/2019/texmf-dist/tex/latex/oberdiek/kvoptions.sty
Package: kvoptions 2016/05/16 v3.12 Key value format for package options (HO)
 (/usr/local/texlive/2019/texmf-dist/tex/generic/oberdiek/ltxcmds.sty
Package: ltxcmds 2016/05/16 v1.23 LaTeX kernel commands for general use (HO)
) (/usr/local/texlive/2019/texmf-dist/tex/generic/oberdiek/kvsetkeys.sty
Package: kvsetkeys 2016/05/16 v1.17 Key value parser (HO)
 (/usr/local/texlive/2019/texmf-dist/tex/generic/oberdiek/infwarerr.sty
Package: infwarerr 2016/05/16 v1.4 Providing info/warning/error messages (HO)
) (/usr/local/texlive/2019/texmf-dist/tex/generic/oberdiek/etexcmds.sty
Package: etexcmds 2016/05/16 v1.6 Avoid name clashes with e-TeX commands (HO)
))) (/usr/local/texlive/2019/texmf-dist/tex/generic/oberdiek/pdftexcmds.sty
Package: pdftexcmds 2018/09/10 v0.29 Utility functions of pdfTeX for LuaTeX (HO)
Package pdftexcmds Info: LuaTeX not detected.
Package pdftexcmds Info: pdfTeX >= 1.30 not detected.
Package pdftexcmds Info: \pdf@primitive is available.
Package pdftexcmds Info: \pdf@ifprimitive is available.
Package pdftexcmds Info: \pdfdraftmode not found.
)
Package grffile Info: Option `multidot' is set to `true'.
Package grffile Info: Option `extendedchars' is set to `false'.
Package grffile Info: Option `space' is set to `true'.
Package grffile Info: \Gin@ii of package `graphicx' fixed on input line 494.
) (/usr/local/texlive/2019/texmf-dist/tex/generic/ulem/ulem.sty
\UL@box=\box49
\UL@hyphenbox=\box50
\UL@skip=\skip56
\UL@hook=\toks26
\UL@height=\dimen160
\UL@pe=\count277
\UL@pixel=\dimen161
\ULC@box=\box51
Package: ulem 2012/05/18
\ULdepth=\dimen162
) (/usr/local/texlive/2019/texmf-dist/tex/latex/hyperref/hyperref.sty
Package: hyperref 2018/11/30 v6.88e Hypertext links for LaTeX
 (/usr/local/texlive/2019/texmf-dist/tex/generic/oberdiek/hobsub-hyperref.sty
Package: hobsub-hyperref 2016/05/16 v1.14 Bundle oberdiek, subset hyperref (HO)
 (/usr/local/texlive/2019/texmf-dist/tex/generic/oberdiek/hobsub-generic.sty
Package: hobsub-generic 2016/05/16 v1.14 Bundle oberdiek, subset generic (HO)
Package: hobsub 2016/05/16 v1.14 Construct package bundles (HO)
Package hobsub Info: Skipping package `infwarerr' (already loaded).
Package hobsub Info: Skipping package `ltxcmds' (already loaded).
Package hobsub Info: Skipping package `ifluatex' (already loaded).
Package: ifvtex 2016/05/16 v1.6 Detect VTeX and its facilities (HO)
Package ifvtex Info: VTeX not detected.
Package: intcalc 2016/05/16 v1.2 Expandable calculations with integers (HO)
Package hobsub Info: Skipping package `ifpdf' (already loaded).
Package hobsub Info: Skipping package `etexcmds' (already loaded).
Package hobsub Info: Skipping package `kvsetkeys' (already loaded).
Package: kvdefinekeys 2016/05/16 v1.4 Define keys (HO)
Package hobsub Info: Skipping package `pdftexcmds' (already loaded).
Package: pdfescape 2016/05/16 v1.14 Implements pdfTeX's escape features (HO)
Package: bigintcalc 2016/05/16 v1.4 Expandable calculations on big integers (HO)
Package: bitset 2016/05/16 v1.2 Handle bit-vector datatype (HO)
Package: uniquecounter 2016/05/16 v1.3 Provide unlimited unique counter (HO)
)
Package hobsub Info: Skipping package `hobsub' (already loaded).
Package: letltxmacro 2016/05/16 v1.5 Let assignment for LaTeX macros (HO)
Package: hopatch 2016/05/16 v1.3 Wrapper for package hooks (HO)
Package: xcolor-patch 2016/05/16 xcolor patch
Package: atveryend 2016/05/16 v1.9 Hooks at the very end of document (HO)
Package: atbegshi 2016/06/09 v1.18 At begin shipout hook (HO)
Package: refcount 2016/05/16 v3.5 Data extraction from label references (HO)
Package: hycolor 2016/05/16 v1.8 Color options for hyperref/bookmark (HO)
) (/usr/local/texlive/2019/texmf-dist/tex/latex/oberdiek/auxhook.sty
Package: auxhook 2016/05/16 v1.4 Hooks for auxiliary files (HO)
)
\@linkdim=\dimen163
\Hy@linkcounter=\count278
\Hy@pagecounter=\count279
 (/usr/local/texlive/2019/texmf-dist/tex/latex/hyperref/pd1enc.def
File: pd1enc.def 2018/11/30 v6.88e Hyperref: PDFDocEncoding definition (HO)
)
\Hy@SavedSpaceFactor=\count280
 (/usr/local/texlive/2019/texmf-dist/tex/latex/latexconfig/hyperref.cfg
File: hyperref.cfg 2002/06/06 v1.2 hyperref configuration of TeXLive
)
Package hyperref Info: Option `unicode' set `true' on input line 4393.
 (/usr/local/texlive/2019/texmf-dist/tex/latex/hyperref/puenc.def
File: puenc.def 2018/11/30 v6.88e Hyperref: PDF Unicode definition (HO)
)
Package hyperref Info: Option `setpagesize' set `false' on input line 4393.
Package hyperref Info: Option `unicode' set `false' on input line 4393.
Package hyperref Info: Option `colorlinks' set `true' on input line 4393.
Package hyperref Info: Hyper figures OFF on input line 4519.
Package hyperref Info: Link nesting OFF on input line 4524.
Package hyperref Info: Hyper index ON on input line 4527.
Package hyperref Info: Plain pages OFF on input line 4534.
Package hyperref Info: Backreferencing OFF on input line 4539.
Package hyperref Info: Implicit mode ON; LaTeX internals redefined.
Package hyperref Info: Bookmarks ON on input line 4772.
\c@Hy@tempcnt=\count281
 (/usr/local/texlive/2019/texmf-dist/tex/latex/url/url.sty
\Urlmuskip=\muskip17
Package: url 2013/09/16  ver 3.4  Verb mode for urls, etc.
)
LaTeX Info: Redefining \url on input line 5125.
\XeTeXLinkMargin=\dimen164
\Fld@menulength=\count282
\Field@Width=\dimen165
\Fld@charsize=\dimen166
Package hyperref Info: Hyper figures OFF on input line 6380.
Package hyperref Info: Link nesting OFF on input line 6385.
Package hyperref Info: Hyper index ON on input line 6388.
Package hyperref Info: backreferencing OFF on input line 6395.
Package hyperref Info: Link coloring ON on input line 6398.
Package hyperref Info: Link coloring with OCG OFF on input line 6405.
Package hyperref Info: PDF/A mode OFF on input line 6410.
LaTeX Info: Redefining \ref on input line 6450.
LaTeX Info: Redefining \pageref on input line 6454.
\Hy@abspage=\count283
\c@Item=\count284
\c@Hfootnote=\count285
)
Package hyperref Info: Driver (autodetected): hxetex.
 (/usr/local/texlive/2019/texmf-dist/tex/latex/hyperref/hxetex.def
File: hxetex.def 2018/11/30 v6.88e Hyperref driver for XeTeX
 (/usr/local/texlive/2019/texmf-dist/tex/generic/oberdiek/stringenc.sty
Package: stringenc 2016/05/16 v1.11 Convert strings between diff. encodings (HO)
)
\pdfm@box=\box52
\c@Hy@AnnotLevel=\count286
\HyField@AnnotCount=\count287
\Fld@listcount=\count288
\c@bookmark@seq@number=\count289
 (/usr/local/texlive/2019/texmf-dist/tex/latex/oberdiek/rerunfilecheck.sty
Package: rerunfilecheck 2016/05/16 v1.8 Rerun checks for auxiliary files (HO)
Package uniquecounter Info: New unique counter `rerunfilecheck' on input line 282.
)
\Hy@SectionHShift=\skip57
) (/usr/local/texlive/2019/texmf-dist/tex/latex/sectsty/sectsty.sty
Package: sectsty 2002/02/25 v2.0.2 Commands to change all sectional heading styles
) (/usr/local/texlive/2019/texmf-dist/tex/latex/xcolor/xcolor.sty
Package: xcolor 2016/05/11 v2.12 LaTeX color extensions (UK)
 (/usr/local/texlive/2019/texmf-dist/tex/latex/graphics-cfg/color.cfg
File: color.cfg 2016/01/02 v1.6 sample color configuration
)
Package xcolor Info: Driver file: xetex.def on input line 225.
Package xcolor Info: Model `cmy' substituted by `cmy0' on input line 1348.
Package xcolor Info: Model `RGB' extended on input line 1364.
Package xcolor Info: Model `HTML' substituted by `rgb' on input line 1366.
Package xcolor Info: Model `Hsb' substituted by `hsb' on input line 1367.
Package xcolor Info: Model `tHsb' substituted by `hsb' on input line 1368.
Package xcolor Info: Model `HSB' substituted by `hsb' on input line 1369.
Package xcolor Info: Model `Gray' substituted by `gray' on input line 1370.
Package xcolor Info: Model `wave' substituted by `hsb' on input line 1371.
) (/usr/local/texlive/2019/texmf-dist/tex/latex/titlesec/titlesec.sty
Package: titlesec 2016/03/21 v2.10.2 Sectioning titles
\ttl@box=\box53
\beforetitleunit=\skip58
\aftertitleunit=\skip59
\ttl@plus=\dimen167
\ttl@minus=\dimen168
\ttl@toksa=\toks27
\titlewidth=\dimen169
\titlewidthlast=\dimen170
\titlewidthfirst=\dimen171
) (/usr/local/texlive/2019/texmf-dist/tex/latex/tools/multicol.sty
Package: multicol 2018/12/27 v1.8v multicolumn formatting (FMi)
\c@tracingmulticols=\count290
\mult@box=\box54
\multicol@leftmargin=\dimen172
\c@unbalance=\count291
\c@collectmore=\count292
\doublecol@number=\count293
\multicoltolerance=\count294
\multicolpretolerance=\count295
\full@width=\dimen173
\page@free=\dimen174
\premulticols=\dimen175
\postmulticols=\dimen176
\multicolsep=\skip60
\multicolbaselineskip=\skip61
\partial@page=\box55
\last@line=\box56
\maxbalancingoverflow=\dimen177
\mult@rightbox=\box57
\mult@grightbox=\box58
\mult@gfirstbox=\box59
\mult@firstbox=\box60
\@tempa=\box61
\@tempa=\box62
\@tempa=\box63
\@tempa=\box64
\@tempa=\box65
\@tempa=\box66
\@tempa=\box67
\@tempa=\box68
\@tempa=\box69
\@tempa=\box70
\@tempa=\box71
\@tempa=\box72
\@tempa=\box73
\@tempa=\box74
\@tempa=\box75
\@tempa=\box76
\@tempa=\box77
\c@columnbadness=\count296
\c@finalcolumnbadness=\count297
\last@try=\dimen178
\multicolovershoot=\dimen179
\multicolundershoot=\dimen180
\mult@nat@firstbox=\box78
\colbreak@box=\box79
\mc@col@check@num=\count298
) (/usr/local/texlive/2019/texmf-dist/tex/latex/pgfgantt/pgfgantt.sty
Package: pgfgantt 2018/01/10 v5.0 Draw Gantt diagrams with TikZ
 (/usr/local/texlive/2019/texmf-dist/tex/latex/pgf/frontendlayer/tikz.sty (/usr/local/texlive/2019/texmf-dist/tex/latex/pgf/basiclayer/pgf.sty (/usr/local/texlive/2019/texmf-dist/tex/latex/pgf/utilities/pgfrcs.sty (/usr/local/texlive/2019/texmf-dist/tex/generic/pgf/utilities/pgfutil-common.tex
\pgfutil@everybye=\toks28
\pgfutil@tempdima=\dimen181
\pgfutil@tempdimb=\dimen182
 (/usr/local/texlive/2019/texmf-dist/tex/generic/pgf/utilities/pgfutil-common-lists.tex)) (/usr/local/texlive/2019/texmf-dist/tex/generic/pgf/utilities/pgfutil-latex.def
\pgfutil@abb=\box80
 (/usr/local/texlive/2019/texmf-dist/tex/latex/ms/everyshi.sty
Package: everyshi 2001/05/15 v3.00 EveryShipout Package (MS)
)) (/usr/local/texlive/2019/texmf-dist/tex/generic/pgf/utilities/pgfrcs.code.tex (/usr/local/texlive/2019/texmf-dist/tex/generic/pgf/pgf.revision.tex)
Package: pgfrcs 2019/04/04 v3.1.2 (3.1.2)
))
Package: pgf 2019/04/04 v3.1.2 (3.1.2)
 (/usr/local/texlive/2019/texmf-dist/tex/latex/pgf/basiclayer/pgfcore.sty (/usr/local/texlive/2019/texmf-dist/tex/latex/pgf/systemlayer/pgfsys.sty (/usr/local/texlive/2019/texmf-dist/tex/generic/pgf/systemlayer/pgfsys.code.tex
Package: pgfsys 2019/04/04 v3.1.2 (3.1.2)
 (/usr/local/texlive/2019/texmf-dist/tex/generic/pgf/utilities/pgfkeys.code.tex
\pgfkeys@pathtoks=\toks29
\pgfkeys@temptoks=\toks30
 (/usr/local/texlive/2019/texmf-dist/tex/generic/pgf/utilities/pgfkeysfiltered.code.tex
\pgfkeys@tmptoks=\toks31
))
\pgf@x=\dimen183
\pgf@y=\dimen184
\pgf@xa=\dimen185
\pgf@ya=\dimen186
\pgf@xb=\dimen187
\pgf@yb=\dimen188
\pgf@xc=\dimen189
\pgf@yc=\dimen190
\w@pgf@writea=\write3
\r@pgf@reada=\read2
\c@pgf@counta=\count299
\c@pgf@countb=\count300
\c@pgf@countc=\count301
\c@pgf@countd=\count302
\t@pgf@toka=\toks32
\t@pgf@tokb=\toks33
\t@pgf@tokc=\toks34
\pgf@sys@id@count=\count303
 (/usr/local/texlive/2019/texmf-dist/tex/generic/pgf/systemlayer/pgf.cfg
File: pgf.cfg 2019/04/04 v3.1.2 (3.1.2)
)
Driver file for pgf: pgfsys-xetex.def
 (/usr/local/texlive/2019/texmf-dist/tex/generic/pgf/systemlayer/pgfsys-xetex.def
File: pgfsys-xetex.def 2019/04/04 v3.1.2 (3.1.2)
 (/usr/local/texlive/2019/texmf-dist/tex/generic/pgf/systemlayer/pgfsys-dvipdfmx.def
File: pgfsys-dvipdfmx.def 2019/04/04 v3.1.2 (3.1.2)
 (/usr/local/texlive/2019/texmf-dist/tex/generic/pgf/systemlayer/pgfsys-common-pdf.def
File: pgfsys-common-pdf.def 2019/04/04 v3.1.2 (3.1.2)
)
\pgfsys@objnum=\count304
))) (/usr/local/texlive/2019/texmf-dist/tex/generic/pgf/systemlayer/pgfsyssoftpath.code.tex
File: pgfsyssoftpath.code.tex 2019/04/04 v3.1.2 (3.1.2)
\pgfsyssoftpath@smallbuffer@items=\count305
\pgfsyssoftpath@bigbuffer@items=\count306
) (/usr/local/texlive/2019/texmf-dist/tex/generic/pgf/systemlayer/pgfsysprotocol.code.tex
File: pgfsysprotocol.code.tex 2019/04/04 v3.1.2 (3.1.2)
)) (/usr/local/texlive/2019/texmf-dist/tex/generic/pgf/basiclayer/pgfcore.code.tex
Package: pgfcore 2019/04/04 v3.1.2 (3.1.2)
 (/usr/local/texlive/2019/texmf-dist/tex/generic/pgf/math/pgfmath.code.tex (/usr/local/texlive/2019/texmf-dist/tex/generic/pgf/math/pgfmathcalc.code.tex (/usr/local/texlive/2019/texmf-dist/tex/generic/pgf/math/pgfmathutil.code.tex) (/usr/local/texlive/2019/texmf-dist/tex/generic/pgf/math/pgfmathparser.code.tex
\pgfmath@dimen=\dimen191
\pgfmath@count=\count307
\pgfmath@box=\box81
\pgfmath@toks=\toks35
\pgfmath@stack@operand=\toks36
\pgfmath@stack@operation=\toks37
) (/usr/local/texlive/2019/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.code.tex (/usr/local/texlive/2019/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.basic.code.tex) (/usr/local/texlive/2019/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.trigonometric.code.tex) (/usr/local/texlive/2019/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.random.code.tex) (/usr/local/texlive/2019/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.comparison.code.tex) (/usr/local/texlive/2019/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.base.code.tex) (/usr/local/texlive/2019/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.round.code.tex) (/usr/local/texlive/2019/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.misc.code.tex) (/usr/local/texlive/2019/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.integerarithmetics.code.tex))) (/usr/local/texlive/2019/texmf-dist/tex/generic/pgf/math/pgfmathfloat.code.tex
\c@pgfmathroundto@lastzeros=\count308
)) (/usr/local/texlive/2019/texmf-dist/tex/generic/pgf/basiclayer/pgfcorepoints.code.tex
File: pgfcorepoints.code.tex 2019/04/04 v3.1.2 (3.1.2)
\pgf@picminx=\dimen192
\pgf@picmaxx=\dimen193
\pgf@picminy=\dimen194
\pgf@picmaxy=\dimen195
\pgf@pathminx=\dimen196
\pgf@pathmaxx=\dimen197
\pgf@pathminy=\dimen198
\pgf@pathmaxy=\dimen199
\pgf@xx=\dimen256
\pgf@xy=\dimen257
\pgf@yx=\dimen258
\pgf@yy=\dimen259
\pgf@zx=\dimen260
\pgf@zy=\dimen261
) (/usr/local/texlive/2019/texmf-dist/tex/generic/pgf/basiclayer/pgfcorepathconstruct.code.tex
File: pgfcorepathconstruct.code.tex 2019/04/04 v3.1.2 (3.1.2)
\pgf@path@lastx=\dimen262
\pgf@path@lasty=\dimen263
) (/usr/local/texlive/2019/texmf-dist/tex/generic/pgf/basiclayer/pgfcorepathusage.code.tex
File: pgfcorepathusage.code.tex 2019/04/04 v3.1.2 (3.1.2)
\pgf@shorten@end@additional=\dimen264
\pgf@shorten@start@additional=\dimen265
) (/usr/local/texlive/2019/texmf-dist/tex/generic/pgf/basiclayer/pgfcorescopes.code.tex
File: pgfcorescopes.code.tex 2019/04/04 v3.1.2 (3.1.2)
\pgfpic=\box82
\pgf@hbox=\box83
\pgf@layerbox@main=\box84
\pgf@picture@serial@count=\count309
) (/usr/local/texlive/2019/texmf-dist/tex/generic/pgf/basiclayer/pgfcoregraphicstate.code.tex
File: pgfcoregraphicstate.code.tex 2019/04/04 v3.1.2 (3.1.2)
\pgflinewidth=\dimen266
) (/usr/local/texlive/2019/texmf-dist/tex/generic/pgf/basiclayer/pgfcoretransformations.code.tex
File: pgfcoretransformations.code.tex 2019/04/04 v3.1.2 (3.1.2)
\pgf@pt@x=\dimen267
\pgf@pt@y=\dimen268
\pgf@pt@temp=\dimen269
) (/usr/local/texlive/2019/texmf-dist/tex/generic/pgf/basiclayer/pgfcorequick.code.tex
File: pgfcorequick.code.tex 2019/04/04 v3.1.2 (3.1.2)
) (/usr/local/texlive/2019/texmf-dist/tex/generic/pgf/basiclayer/pgfcoreobjects.code.tex
File: pgfcoreobjects.code.tex 2019/04/04 v3.1.2 (3.1.2)
) (/usr/local/texlive/2019/texmf-dist/tex/generic/pgf/basiclayer/pgfcorepathprocessing.code.tex
File: pgfcorepathprocessing.code.tex 2019/04/04 v3.1.2 (3.1.2)
) (/usr/local/texlive/2019/texmf-dist/tex/generic/pgf/basiclayer/pgfcorearrows.code.tex
File: pgfcorearrows.code.tex 2019/04/04 v3.1.2 (3.1.2)
\pgfarrowsep=\dimen270
) (/usr/local/texlive/2019/texmf-dist/tex/generic/pgf/basiclayer/pgfcoreshade.code.tex
File: pgfcoreshade.code.tex 2019/04/04 v3.1.2 (3.1.2)
\pgf@max=\dimen271
\pgf@sys@shading@range@num=\count310
\pgf@shadingcount=\count311
) (/usr/local/texlive/2019/texmf-dist/tex/generic/pgf/basiclayer/pgfcoreimage.code.tex
File: pgfcoreimage.code.tex 2019/04/04 v3.1.2 (3.1.2)
 (/usr/local/texlive/2019/texmf-dist/tex/generic/pgf/basiclayer/pgfcoreexternal.code.tex
File: pgfcoreexternal.code.tex 2019/04/04 v3.1.2 (3.1.2)
\pgfexternal@startupbox=\box85
)) (/usr/local/texlive/2019/texmf-dist/tex/generic/pgf/basiclayer/pgfcorelayers.code.tex
File: pgfcorelayers.code.tex 2019/04/04 v3.1.2 (3.1.2)
) (/usr/local/texlive/2019/texmf-dist/tex/generic/pgf/basiclayer/pgfcoretransparency.code.tex
File: pgfcoretransparency.code.tex 2019/04/04 v3.1.2 (3.1.2)
) (/usr/local/texlive/2019/texmf-dist/tex/generic/pgf/basiclayer/pgfcorepatterns.code.tex
File: pgfcorepatterns.code.tex 2019/04/04 v3.1.2 (3.1.2)
) (/usr/local/texlive/2019/texmf-dist/tex/generic/pgf/basiclayer/pgfcorerdf.code.tex
File: pgfcorerdf.code.tex 2019/04/04 v3.1.2 (3.1.2)
))) (/usr/local/texlive/2019/texmf-dist/tex/generic/pgf/modules/pgfmoduleshapes.code.tex
File: pgfmoduleshapes.code.tex 2019/04/04 v3.1.2 (3.1.2)
\pgfnodeparttextbox=\box86
) (/usr/local/texlive/2019/texmf-dist/tex/generic/pgf/modules/pgfmoduleplot.code.tex
File: pgfmoduleplot.code.tex 2019/04/04 v3.1.2 (3.1.2)
) (/usr/local/texlive/2019/texmf-dist/tex/latex/pgf/compatibility/pgfcomp-version-0-65.sty
Package: pgfcomp-version-0-65 2019/04/04 v3.1.2 (3.1.2)
\pgf@nodesepstart=\dimen272
\pgf@nodesepend=\dimen273
) (/usr/local/texlive/2019/texmf-dist/tex/latex/pgf/compatibility/pgfcomp-version-1-18.sty
Package: pgfcomp-version-1-18 2019/04/04 v3.1.2 (3.1.2)
)) (/usr/local/texlive/2019/texmf-dist/tex/latex/pgf/utilities/pgffor.sty (/usr/local/texlive/2019/texmf-dist/tex/latex/pgf/utilities/pgfkeys.sty (/usr/local/texlive/2019/texmf-dist/tex/generic/pgf/utilities/pgfkeys.code.tex)) (/usr/local/texlive/2019/texmf-dist/tex/latex/pgf/math/pgfmath.sty (/usr/local/texlive/2019/texmf-dist/tex/generic/pgf/math/pgfmath.code.tex)) (/usr/local/texlive/2019/texmf-dist/tex/generic/pgf/utilities/pgffor.code.tex
Package: pgffor 2019/04/04 v3.1.2 (3.1.2)
 (/usr/local/texlive/2019/texmf-dist/tex/generic/pgf/math/pgfmath.code.tex)
\pgffor@iter=\dimen274
\pgffor@skip=\dimen275
\pgffor@stack=\toks38
\pgffor@toks=\toks39
)) (/usr/local/texlive/2019/texmf-dist/tex/generic/pgf/frontendlayer/tikz/tikz.code.tex
Package: tikz 2019/04/04 v3.1.2 (3.1.2)
 (/usr/local/texlive/2019/texmf-dist/tex/generic/pgf/libraries/pgflibraryplothandlers.code.tex
File: pgflibraryplothandlers.code.tex 2019/04/04 v3.1.2 (3.1.2)
\pgf@plot@mark@count=\count312
\pgfplotmarksize=\dimen276
)
\tikz@lastx=\dimen277
\tikz@lasty=\dimen278
\tikz@lastxsaved=\dimen279
\tikz@lastysaved=\dimen280
\tikzleveldistance=\dimen281
\tikzsiblingdistance=\dimen282
\tikz@figbox=\box87
\tikz@figbox@bg=\box88
\tikz@tempbox=\box89
\tikz@tempbox@bg=\box90
\tikztreelevel=\count313
\tikznumberofchildren=\count314
\tikznumberofcurrentchild=\count315
\tikz@fig@count=\count316
 (/usr/local/texlive/2019/texmf-dist/tex/generic/pgf/modules/pgfmodulematrix.code.tex
File: pgfmodulematrix.code.tex 2019/04/04 v3.1.2 (3.1.2)
\pgfmatrixcurrentrow=\count317
\pgfmatrixcurrentcolumn=\count318
\pgf@matrix@numberofcolumns=\count319
)
\tikz@expandcount=\count320
 (/usr/local/texlive/2019/texmf-dist/tex/generic/pgf/frontendlayer/tikz/libraries/tikzlibrarytopaths.code.tex
File: tikzlibrarytopaths.code.tex 2019/04/04 v3.1.2 (3.1.2)
))) (/usr/local/texlive/2019/texmf-dist/tex/generic/pgf/frontendlayer/tikz/libraries/tikzlibraryarrows.code.tex
File: tikzlibraryarrows.code.tex 2019/04/04 v3.1.2 (3.1.2)
 (/usr/local/texlive/2019/texmf-dist/tex/generic/pgf/libraries/pgflibraryarrows.code.tex
File: pgflibraryarrows.code.tex 2019/04/04 v3.1.2 (3.1.2)
\arrowsize=\dimen283
)) (/usr/local/texlive/2019/texmf-dist/tex/generic/pgf/frontendlayer/tikz/libraries/tikzlibrarybackgrounds.code.tex
File: tikzlibrarybackgrounds.code.tex 2019/04/04 v3.1.2 (3.1.2)
\pgf@layerbox@background=\box91
\pgf@layerboxsaved@background=\box92
) (/usr/local/texlive/2019/texmf-dist/tex/generic/pgf/frontendlayer/tikz/libraries/tikzlibrarycalc.code.tex
File: tikzlibrarycalc.code.tex 2019/04/04 v3.1.2 (3.1.2)
) (/usr/local/texlive/2019/texmf-dist/tex/generic/pgf/frontendlayer/tikz/libraries/tikzlibrarypatterns.code.tex
File: tikzlibrarypatterns.code.tex 2019/04/04 v3.1.2 (3.1.2)
 (/usr/local/texlive/2019/texmf-dist/tex/generic/pgf/libraries/pgflibrarypatterns.code.tex
File: pgflibrarypatterns.code.tex 2019/04/04 v3.1.2 (3.1.2)
)) (/usr/local/texlive/2019/texmf-dist/tex/generic/pgf/frontendlayer/tikz/libraries/tikzlibrarypositioning.code.tex
File: tikzlibrarypositioning.code.tex 2019/04/04 v3.1.2 (3.1.2)
) (/usr/local/texlive/2019/texmf-dist/tex/generic/pgf/frontendlayer/tikz/libraries/tikzlibraryshapes.geometric.code.tex
File: tikzlibraryshapes.geometric.code.tex 2019/04/04 v3.1.2 (3.1.2)
 (/usr/local/texlive/2019/texmf-dist/tex/generic/pgf/libraries/shapes/pgflibraryshapes.geometric.code.tex
File: pgflibraryshapes.geometric.code.tex 2019/04/04 v3.1.2 (3.1.2)
))
(/usr/local/texlive/2019/texmf-dist/tex/latex/pgf/utilities/pgfcalendar.sty (/usr/local/texlive/2019/texmf-dist/tex/generic/pgf/utilities/pgfcalendar.code.tex
File: pgfcalendar.code.tex 2019/04/04 v3.1.2 (3.1.2)
\pgfcalendarcurrentjulian=\count321
))
\gtt@currentline=\count322
\gtt@lasttitleline=\count323
\gtt@currgrid=\count324
\gtt@chartwidth=\count325
\gtt@lasttitleslot=\count326
\gtt@elementid=\count327
\gtt@today@slot=\count328
\gtt@startjulian=\count329
\gtt@endjulian=\count330
\gtt@chartid=\count331
\gtt@vrule@slot=\count332
\gtt@calendar@slots=\count333
\gtt@calendar@weeknumber=\count334
\gtt@calendar@startofweek=\count335
\gtt@left@slot=\count336
\gtt@right@slot=\count337
) (/usr/local/texlive/2019/texmf-dist/tex/latex/graphics/rotating.sty
Package: rotating 2016/08/11 v2.16d rotated objects in LaTeX
 (/usr/local/texlive/2019/texmf-dist/tex/latex/base/ifthen.sty
Package: ifthen 2014/09/29 v1.1c Standard LaTeX ifthen package (DPC)
)
\c@r@tfl@t=\count338
\rotFPtop=\skip62
\rotFPbot=\skip63
\rot@float@box=\box93
\rot@mess@toks=\toks40
) (/usr/local/texlive/2019/texmf-dist/tex/latex/multirow/multirow.sty
Package: multirow 2019/01/01 v2.4 Span multiple rows of a table
\multirow@colwidth=\skip64
\multirow@cntb=\count339
\multirow@dima=\skip65
\bigstrutjot=\dimen284
) (/usr/local/texlive/2019/texmf-dist/tex/latex/tools/longtable.sty
Package: longtable 2014/10/28 v4.11 Multi-page Table package (DPC)+ FMi change
\LTleft=\skip66
\LTright=\skip67
\LTpre=\skip68
\LTpost=\skip69
\LTchunksize=\count340
\LTcapwidth=\dimen285
\LT@head=\box94
\LT@firsthead=\box95
\LT@foot=\box96
\LT@lastfoot=\box97
\LT@cols=\count341
\LT@rows=\count342
\c@LT@tables=\count343
\c@LT@chunks=\count344
\LT@p@ftn=\toks41
) (/usr/local/texlive/2019/texmf-dist/tex/latex/booktabs/booktabs.sty
Package: booktabs 2016/04/27 v1.618033 publication quality tables
\heavyrulewidth=\dimen286
\lightrulewidth=\dimen287
\cmidrulewidth=\dimen288
\belowrulesep=\dimen289
\belowbottomsep=\dimen290
\aboverulesep=\dimen291
\abovetopsep=\dimen292
\cmidrulesep=\dimen293
\cmidrulekern=\dimen294
\defaultaddspace=\dimen295
\@cmidla=\count345
\@cmidlb=\count346
\@aboverulesep=\dimen296
\@belowrulesep=\dimen297
\@thisruleclass=\count347
\@lastruleclass=\count348
\@thisrulewidth=\dimen298
) (/usr/local/texlive/2019/texmf-dist/tex/latex/base/fontenc.sty
Package: fontenc 2018/08/11 v2.0j Standard LaTeX package
 (/usr/local/texlive/2019/texmf-dist/tex/latex/base/t1enc.def
File: t1enc.def 2018/08/11 v2.0j Standard LaTeX file
LaTeX Font Info:    Redeclaring font encoding T1 on input line 48.
)
LaTeX Font Info:    Try loading font information for T1+lmr on input line 105.
 (/usr/local/texlive/2019/texmf-dist/tex/latex/lm/t1lmr.fd
File: t1lmr.fd 2009/10/30 v1.6 Font defs for Latin Modern
)) (/usr/local/texlive/2019/texmf-dist/tex/latex/setspace/setspace.sty
Package: setspace 2011/12/19 v6.7a set line spacing
) (/usr/local/texlive/2019/texmf-dist/tex/latex/listings/listings.sty
\lst@mode=\count349
\lst@gtempboxa=\box98
\lst@token=\toks42
\lst@length=\count350
\lst@currlwidth=\dimen299
\lst@column=\count351
\lst@pos=\count352
\lst@lostspace=\dimen300
\lst@width=\dimen301
\lst@newlines=\count353
\lst@lineno=\count354
\lst@maxwidth=\dimen302
 (/usr/local/texlive/2019/texmf-dist/tex/latex/listings/lstmisc.sty
File: lstmisc.sty 2019/02/27 1.8b (Carsten Heinz)
\c@lstnumber=\count355
\lst@skipnumbers=\count356
\lst@framebox=\box99
) (/usr/local/texlive/2019/texmf-dist/tex/latex/listings/listings.cfg
File: listings.cfg 2019/02/27 1.8b listings configuration
))
Package: listings 2019/02/27 1.8b (Carsten Heinz)


/Users/<USER>/Sites/Elixir/wraft-docs-api/lib/slugs/contract/template.tex:53: LaTeX Error: Missing \begin{document}.

See the LaTeX manual or LaTeX Companion for explanation.
Type  H <return>  for immediate help.
 ...

l.53 $
      if(path)$
You're in trouble here.  Try typing  <return>  to proceed.
If that doesn't work, type  X <return>  to quit.

LaTeX Font Info:    Try loading font information for U+msa on input line 53.
(/usr/local/texlive/2019/texmf-dist/tex/latex/amsfonts/umsa.fd
File: umsa.fd 2013/01/14 v3.01 AMS symbols A
)
LaTeX Font Info:    Try loading font information for U+msb on input line 53.
 (/usr/local/texlive/2019/texmf-dist/tex/latex/amsfonts/umsb.fd
File: umsb.fd 2013/01/14 v3.01 AMS symbols B
)

/Users/<USER>/Sites/Elixir/wraft-docs-api/lib/slugs/contract/template.tex:57: Package fontspec Error: The font "Mallory-Book" cannot be found.

For immediate help type H <return>.
 ...

l.57 $
      endif$

A font might not be found for many reasons.
 Check the spelling, where the font is installed etc. etc.

 When in doubt, ask someone for help!



/Users/<USER>/Sites/Elixir/wraft-docs-api/lib/slugs/contract/template.tex:57: Package fontspec Error: The font "Mallory-Book" cannot be found.

For immediate help type H <return>.
 ...

l.57 $
      endif$

A font might not be found for many reasons.
 Check the spelling, where the font is installed etc. etc.

 When in doubt, ask someone for help!



/Users/<USER>/Sites/Elixir/wraft-docs-api/lib/slugs/contract/template.tex:57: Package fontspec Error: The font "Mallory-Book" cannot be found.

For immediate help type H <return>.
 ...

l.57 $
      endif$

A font might not be found for many reasons.
 Check the spelling, where the font is installed etc. etc.

 When in doubt, ask someone for help!



/Users/<USER>/Sites/Elixir/wraft-docs-api/lib/slugs/contract/template.tex:57: Package fontspec Error: The font "Mallory-Bold.otf" cannot be found.

For immediate help type H <return>.
 ...

l.57 $
      endif$

A font might not be found for many reasons.
 Check the spelling, where the font is installed etc. etc.

 When in doubt, ask someone for help!



/Users/<USER>/Sites/Elixir/wraft-docs-api/lib/slugs/contract/template.tex:57: Package fontspec Error: The font "Mallory-Bold" cannot be found.

For immediate help type H <return>.
 ...

l.57 $
      endif$

A font might not be found for many reasons.
 Check the spelling, where the font is installed etc. etc.

 When in doubt, ask someone for help!



Package fontspec Info: Font family 'Mallory-Book.otf(0)' created for font
(fontspec)             'Mallory-Book.otf' with options [Ligatures=TeX,Path =
(fontspec)             $path$/fonts/,BoldFont = Mallory-Bold.otf].
(fontspec)
(fontspec)              This font family consists of the following NFSS
(fontspec)             series/shapes:
(fontspec)
(fontspec)             - 'normal' (m/n) with NFSS spec.:
(fontspec)             <->"[$path$/fonts/Mallory-Book.otf]/OT"
(fontspec)             - 'small caps'  (m/sc) with NFSS spec.:
(fontspec)             <->"[$path$/fonts/Mallory-Book.otf]/OT"
(fontspec)             - 'bold' (bx/n) with NFSS spec.:
(fontspec)             <->"[$path$/fonts/Mallory-Bold.otf]/OT"
(fontspec)             - 'bold small caps'  (bx/sc) with NFSS spec.:
(fontspec)             <->"[$path$/fonts/Mallory-Bold.otf]/OT"

LaTeX Info: Redefining \rmfamily on input line 57.
/Users/<USER>/Sites/Elixir/wraft-docs-api/lib/slugs/contract/template.tex:57: Font TU/Mallory-Book.otf(0)/m/n/10.95=[$path$/fonts/Mallory-Book.otf]/OT at 10.95pt not loadable: Metric (TFM) file or installed font not found.
<to be read again>
                   relax
l.57 $
      endif$
I wasn't able to read the size data for this font,
so I will ignore the font specification.
[Wizards can fix TFM files using TFtoPL/PLtoTF.]
You might try inserting a different font spec;
e.g., type `I\font<same font id>=<substitute font name>'.

(/usr/local/texlive/2019/texmf-dist/tex/latex/polyglossia/gloss-english.ldf
File: gloss-english.ldf polyglossia: module for english
)
Package polyglossia Info: Default language is english.


/Users/<USER>/Sites/Elixir/wraft-docs-api/lib/slugs/contract/template.tex:73: LaTeX Error: Missing \begin{document}.

See the LaTeX manual or LaTeX Companion for explanation.
Type  H <return>  for immediate help.
 ...

l.73 $
      if(title)$
You're in trouble here.  Try typing  <return>  to proceed.
If that doesn't work, type  X <return>  to quit.


/Users/<USER>/Sites/Elixir/wraft-docs-api/lib/slugs/contract/template.tex:77: LaTeX Error: Missing \begin{document}.

See the LaTeX manual or LaTeX Companion for explanation.
Type  H <return>  for immediate help.
 ...

l.77 $
      if(id)$
You're in trouble here.  Try typing  <return>  to proceed.
If that doesn't work, type  X <return>  to quit.


/Users/<USER>/Sites/Elixir/wraft-docs-api/lib/slugs/contract/template.tex:83: LaTeX Error: Missing \begin{document}.

See the LaTeX manual or LaTeX Companion for explanation.
Type  H <return>  for immediate help.
 ...

l.83 $
      highlighting-macros$
You're in trouble here.  Try typing  <return>  to proceed.
If that doesn't work, type  X <return>  to quit.

(/usr/local/texlive/2019/texmf-dist/tex/latex/arydshln/arydshln.sty
Package: arydshln 2019/02/21 v1.76
\dashlinedash=\dimen303
\dashlinegap=\dimen304
\adl@box=\box100
\adl@height=\dimen305
\adl@heightsave=\dimen306
\adl@depth=\dimen307
\adl@depthsave=\dimen308
\adl@finaldepth=\dimen309
\adl@columns=\count357
\adl@ncol=\count358
\adl@currentcolumn=\count359
\adl@currentcolumnsave=\count360
\adl@totalheight=\count361
\adl@totalheightsave=\count362
\adl@dash=\count363
\adl@gap=\count364
\adl@cla=\count365
\adl@clb=\count366
\adl@everyvbox=\toks43
\adl@LTpagetotal=\dimen310
) (/usr/local/texlive/2019/texmf-dist/tex/latex/floatrow/floatrow.sty
Package: floatrow 2008/08/02 v0.3b floatrow: float package extension
 (/usr/local/texlive/2019/texmf-dist/tex/latex/caption/caption3.sty
Package: caption3 2018/09/12 v1.8c caption3 kernel (AR)
Package caption3 Info: TeX engine: e-TeX on input line 64.
\captionmargin=\dimen311
\captionmargin@=\dimen312
\captionwidth=\dimen313
\caption@tempdima=\dimen314
\caption@indent=\dimen315
\caption@parindent=\dimen316
\caption@hangindent=\dimen317
)
\c@float@type=\count367
\float@exts=\toks44
\float@box=\box101
\@floatcapt=\box102
Package floatrow Info: Modified float package code loaded on input line 455.
Package floatrow Info: Modified rotfloat package code loaded on input line 473.
\FR@everyfloat=\toks45
\flrow@foot=\insert252
\FB@wd=\dimen318
\FBo@wd=\dimen319
\FBc@wd=\dimen320
\FBo@ht=\skip70
\FBc@ht=\skip71
\FBf@ht=\skip72
\FBo@max=\skip73
\FBc@max=\skip74
\FBf@max=\skip75
\c@FBl@b=\count368
\floatbox@depth=\count369
\c@FRobj=\count370
\c@FRsobj=\count371
\Xhsize=\skip76
\sXhsize=\skip77
\Zhsize=\skip78
\sZhsize=\skip79
\flrow@rowbox=\box103
\FR@Zunitlength=\dimen321
\c@FBcnt=\count372
\FPOScnt=\count373
\LTleft=\skip80
\LTright=\skip81
\LTleft=\skip82
\LTright=\skip83
\flrow@types=\toks46
)
No file template.aux.
\openout1 = `template.aux'.

LaTeX Font Info:    Checking defaults for OML/cmm/m/it on input line 135.
LaTeX Font Info:    ... okay on input line 135.
LaTeX Font Info:    Checking defaults for T1/cmr/m/n on input line 135.
LaTeX Font Info:    ... okay on input line 135.
LaTeX Font Info:    Checking defaults for OT1/cmr/m/n on input line 135.
LaTeX Font Info:    ... okay on input line 135.
LaTeX Font Info:    Checking defaults for OMS/cmsy/m/n on input line 135.
LaTeX Font Info:    ... okay on input line 135.
LaTeX Font Info:    Checking defaults for TU/lmr/m/n on input line 135.
LaTeX Font Info:    ... okay on input line 135.
LaTeX Font Info:    Checking defaults for OMX/cmex/m/n on input line 135.
LaTeX Font Info:    ... okay on input line 135.
LaTeX Font Info:    Checking defaults for U/cmr/m/n on input line 135.
LaTeX Font Info:    ... okay on input line 135.
LaTeX Font Info:    Checking defaults for TS1/cmr/m/n on input line 135.
LaTeX Font Info:    Try loading font information for TS1+cmr on input line 135.
(/usr/local/texlive/2019/texmf-dist/tex/latex/base/ts1cmr.fd
File: ts1cmr.fd 2014/09/29 v2.5h Standard LaTeX font definitions
)
LaTeX Font Info:    ... okay on input line 135.
LaTeX Font Info:    Checking defaults for PD1/pdf/m/n on input line 135.
LaTeX Font Info:    ... okay on input line 135.
LaTeX Font Info:    Checking defaults for PU/pdf/m/n on input line 135.
LaTeX Font Info:    ... okay on input line 135.

Package fontspec Info: Adjusting the maths setup (use [no-math] to avoid
(fontspec)             this).

\symlegacymaths=\mathgroup6
LaTeX Font Info:    Overwriting symbol font `legacymaths' in version `bold'
(Font)                  OT1/cmr/m/n --> OT1/cmr/bx/n on input line 135.
LaTeX Font Info:    Redeclaring math accent \acute on input line 135.
LaTeX Font Info:    Redeclaring math accent \grave on input line 135.
LaTeX Font Info:    Redeclaring math accent \ddot on input line 135.
LaTeX Font Info:    Redeclaring math accent \tilde on input line 135.
LaTeX Font Info:    Redeclaring math accent \bar on input line 135.
LaTeX Font Info:    Redeclaring math accent \breve on input line 135.
LaTeX Font Info:    Redeclaring math accent \check on input line 135.
LaTeX Font Info:    Redeclaring math accent \hat on input line 135.
LaTeX Font Info:    Redeclaring math accent \dot on input line 135.
LaTeX Font Info:    Redeclaring math accent \mathring on input line 135.
LaTeX Font Info:    Redeclaring math symbol \Gamma on input line 135.
LaTeX Font Info:    Redeclaring math symbol \Delta on input line 135.
LaTeX Font Info:    Redeclaring math symbol \Theta on input line 135.
LaTeX Font Info:    Redeclaring math symbol \Lambda on input line 135.
LaTeX Font Info:    Redeclaring math symbol \Xi on input line 135.
LaTeX Font Info:    Redeclaring math symbol \Pi on input line 135.
LaTeX Font Info:    Redeclaring math symbol \Sigma on input line 135.
LaTeX Font Info:    Redeclaring math symbol \Upsilon on input line 135.
LaTeX Font Info:    Redeclaring math symbol \Phi on input line 135.
LaTeX Font Info:    Redeclaring math symbol \Psi on input line 135.
LaTeX Font Info:    Redeclaring math symbol \Omega on input line 135.
LaTeX Font Info:    Redeclaring math symbol \mathdollar on input line 135.
LaTeX Font Info:    Redeclaring symbol font `operators' on input line 135.
LaTeX Font Info:    Encoding `OT1' has changed to `TU' for symbol font
(Font)              `operators' in the math version `normal' on input line 135.
LaTeX Font Info:    Overwriting symbol font `operators' in version `normal'
(Font)                  OT1/cmr/m/n --> TU/Mallory-Book.otf(0)/m/n on input line 135.
LaTeX Font Info:    Encoding `OT1' has changed to `TU' for symbol font
(Font)              `operators' in the math version `bold' on input line 135.
LaTeX Font Info:    Overwriting symbol font `operators' in version `bold'
(Font)                  OT1/cmr/bx/n --> TU/Mallory-Book.otf(0)/m/n on input line 135.
LaTeX Font Info:    Overwriting symbol font `operators' in version `normal'
(Font)                  TU/Mallory-Book.otf(0)/m/n --> TU/Mallory-Book.otf(0)/m/n on input line 135.
LaTeX Font Info:    Overwriting math alphabet `\mathit' in version `normal'
(Font)                  OT1/cmr/m/it --> TU/Mallory-Book.otf(0)/m/it on input line 135.
LaTeX Font Info:    Overwriting math alphabet `\mathbf' in version `normal'
(Font)                  OT1/cmr/bx/n --> TU/Mallory-Book.otf(0)/bx/n on input line 135.
LaTeX Font Info:    Overwriting math alphabet `\mathsf' in version `normal'
(Font)                  OT1/cmss/m/n --> TU/lmss/m/n on input line 135.
LaTeX Font Info:    Overwriting math alphabet `\mathtt' in version `normal'
(Font)                  OT1/cmtt/m/n --> TU/lmtt/m/n on input line 135.
LaTeX Font Info:    Overwriting symbol font `operators' in version `bold'
(Font)                  TU/Mallory-Book.otf(0)/m/n --> TU/Mallory-Book.otf(0)/bx/n on input line 135.
LaTeX Font Info:    Overwriting math alphabet `\mathit' in version `bold'
(Font)                  OT1/cmr/bx/it --> TU/Mallory-Book.otf(0)/bx/it on input line 135.
LaTeX Font Info:    Overwriting math alphabet `\mathsf' in version `bold'
(Font)                  OT1/cmss/bx/n --> TU/lmss/bx/n on input line 135.
LaTeX Font Info:    Overwriting math alphabet `\mathtt' in version `bold'
(Font)                  OT1/cmtt/m/n --> TU/lmtt/bx/n on input line 135.
LaTeX Info: Redefining \microtypecontext on input line 135.
Package microtype Info: Character protrusion enabled (level 2).
Package microtype Info: Using default protrusion set `alltext'.
Package microtype Info: No adjustment of tracking.
Package microtype Info: No adjustment of spacing.
Package microtype Info: No adjustment of kerning.
Package microtype Info: Loading generic protrusion settings for font family
(microtype)             `Mallory-Book.otf' (encoding: TU).
(microtype)             For optimal results, create family-specific settings.
(microtype)             See the microtype manual for details.


Package microtype Warning: Font `TU/Mallory-Book.otf(0)/m/n/10.95' does not specify its
(microtype)                \fontdimen 6 (width of an `em')! Therefore,
(microtype)                protrusion will not work with this font.

\AtBeginShipoutBox=\box104
Package hyperref Info: Link coloring ON on input line 135.
(/usr/local/texlive/2019/texmf-dist/tex/latex/hyperref/nameref.sty
Package: nameref 2016/05/21 v2.44 Cross-referencing by name of section
 (/usr/local/texlive/2019/texmf-dist/tex/generic/oberdiek/gettitlestring.sty
Package: gettitlestring 2016/05/16 v1.5 Cleanup title references (HO)
)
\c@section@level=\count374
)
LaTeX Info: Redefining \ref on input line 135.
LaTeX Info: Redefining \pageref on input line 135.
LaTeX Info: Redefining \nameref on input line 135.
\@outlinefile=\write4
\openout4 = `template.out'.



Package hyperref Warning: Rerun to get /PageLabels entry.

ABD: EveryShipout initializing macros
\c@lstlisting=\count375
Package caption Info: Begin \AtBeginDocument code.
Package caption Info: End \AtBeginDocument code.
 (/usr/local/texlive/2019/texmf-dist/tex/latex/floatrow/fr-longtable.sty
Package: fr-longtable 2007/11/28 v0.1b (beta) floatrow: additions for longtable
\flrow@LT@lasthead=\box105
\flrow@LT@prelastfoot=\box106
\c@FBLTpage=\count376
) (./cover.tex
Missing character: There is no 1 in font nullfont!
 (/usr/local/texlive/2019/texmf-dist/tex/generic/oberdiek/se-ascii-print.def
File: se-ascii-print.def 2016/05/16 v1.11 stringenc: Printable ASCII characters
) [1

]
Missing character: There is no F in font nullfont!
Missing character: There is no u in font nullfont!
Missing character: There is no n in font nullfont!
Missing character: There is no c in font nullfont!
Missing character: There is no t in font nullfont!
Missing character: There is no i in font nullfont!
Missing character: There is no o in font nullfont!
Missing character: There is no n in font nullfont!
Missing character: There is no a in font nullfont!
Missing character: There is no r in font nullfont!
Missing character: There is no y in font nullfont!
Missing character: There is no L in font nullfont!
Missing character: There is no a in font nullfont!
Missing character: There is no b in font nullfont!
Missing character: There is no s in font nullfont!
Missing character: There is no P in font nullfont!
Missing character: There is no v in font nullfont!
Missing character: There is no t in font nullfont!
Missing character: There is no L in font nullfont!
Missing character: There is no t in font nullfont!
Missing character: There is no d in font nullfont!

./cover.tex:5: Font TU/BasierCircle-Regular.otf(0)/m/sc/10.95=[$path$/fonts/BasierCircle-Regular.otf]/OT at 10.95pt not loadable: Metric (TFM) file or installed font not found.
<to be read again>
                   relax
l.5     \textsc{\Large \newCommandProposalId}
                                             \\[0.5cm]
I wasn't able to read the size data for this font,
so I will ignore the font specification.
[Wizards can fix TFM files using TFtoPL/PLtoTF.]
You might try inserting a different font spec;
e.g., type `I\font<same font id>=<substitute font name>'.


Package microtype Warning: Font `TU/BasierCircle-Regular.otf(0)/m/sc/10.95' does not specify its
(microtype)                \fontdimen 6 (width of an `em')! Therefore,
(microtype)                protrusion will not work with this font.

./cover.tex:5: Font TU/BasierCircle-Regular.otf(0)/m/sc/14.4=[$path$/fonts/BasierCircle-Regular.otf]/OT at 14.4pt not loadable: Metric (TFM) file or installed font not found.
<to be read again>
                   relax
l.5     \textsc{\Large \newCommandProposalId}
                                             \\[0.5cm]
I wasn't able to read the size data for this font,
so I will ignore the font specification.
[Wizards can fix TFM files using TFtoPL/PLtoTF.]
You might try inserting a different font spec;
e.g., type `I\font<same font id>=<substitute font name>'.


Package microtype Warning: Font `TU/BasierCircle-Regular.otf(0)/m/sc/14.4' does not specify its
(microtype)                \fontdimen 6 (width of an `em')! Therefore,
(microtype)                protrusion will not work with this font.

./cover.tex:5: Undefined control sequence.
<argument> \Large \newCommandProposalId

l.5     \textsc{\Large \newCommandProposalId}
                                             \\[0.5cm]
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

./cover.tex:8: Font TU/BasierCircle-Regular.otf(0)/m/n/20.74=[$path$/fonts/BasierCircle-Regular.otf]/OT at 20.74pt not loadable: Metric (TFM) file or installed font not found.
<to be read again>
                   relax
l.8     {\huge
               \bfseries \newCommandProposalTitle \\[0.4cm]}
I wasn't able to read the size data for this font,
so I will ignore the font specification.
[Wizards can fix TFM files using TFtoPL/PLtoTF.]
You might try inserting a different font spec;
e.g., type `I\font<same font id>=<substitute font name>'.


Package microtype Warning: Font `TU/BasierCircle-Regular.otf(0)/m/n/20.74' does not specify its
(microtype)                \fontdimen 6 (width of an `em')! Therefore,
(microtype)                protrusion will not work with this font.

./cover.tex:8: Font TU/BasierCircle-Regular.otf(0)/bx/n/20.74=[$path$/fonts/BasierCircle-Medium.otf]/OT at 20.74pt not loadable: Metric (TFM) file or installed font not found.
<to be read again>
                   relax
l.8     {\huge \bfseries
                         \newCommandProposalTitle \\[0.4cm]}
I wasn't able to read the size data for this font,
so I will ignore the font specification.
[Wizards can fix TFM files using TFtoPL/PLtoTF.]
You might try inserting a different font spec;
e.g., type `I\font<same font id>=<substitute font name>'.


Package microtype Warning: Font `TU/BasierCircle-Regular.otf(0)/bx/n/20.74' does not specify its
(microtype)                \fontdimen 6 (width of an `em')! Therefore,
(microtype)                protrusion will not work with this font.

Missing character: There is no P in font nullfont!
Missing character: There is no r in font nullfont!
Missing character: There is no o in font nullfont!
Missing character: There is no p in font nullfont!
Missing character: There is no o in font nullfont!
Missing character: There is no s in font nullfont!
Missing character: There is no a in font nullfont!
Missing character: There is no l in font nullfont!
Missing character: There is no f in font nullfont!
Missing character: There is no o in font nullfont!
Missing character: There is no r in font nullfont!
Missing character: There is no 2 in font nullfont!
Missing character: There is no 0 in font nullfont!
Missing character: There is no 1 in font nullfont!
Missing character: There is no 9 in font nullfont!
Missing character: There is no I in font nullfont!
Missing character: There is no K in font nullfont!
Missing character: There is no S in font nullfont!
Missing character: There is no T in font nullfont!
Missing character: There is no W in font nullfont!
Missing character: There is no e in font nullfont!
Missing character: There is no b in font nullfont!
Missing character: There is no s in font nullfont!
Missing character: There is no i in font nullfont!
Missing character: There is no t in font nullfont!
Missing character: There is no e in font nullfont!
Missing character: There is no R in font nullfont!
Missing character: There is no e in font nullfont!
Missing character: There is no b in font nullfont!
Missing character: There is no u in font nullfont!
Missing character: There is no i in font nullfont!
Missing character: There is no l in font nullfont!
Missing character: There is no d in font nullfont!
Missing character: There is no i in font nullfont!
Missing character: There is no n in font nullfont!
Missing character: There is no g in font nullfont!
Missing character: There is no P in font nullfont!
Missing character: There is no r in font nullfont!
Missing character: There is no o in font nullfont!
Missing character: There is no j in font nullfont!
Missing character: There is no e in font nullfont!
Missing character: There is no c in font nullfont!
Missing character: There is no t in font nullfont!
./cover.tex:11: Font TU/BasierCircle-Regular.otf(0)/m/it/10.95=[$path$/fonts/BasierCircle-RegularItalic.otf]/OT at 10.95pt not loadable: Metric (TFM) file or installed font not found.
<to be read again>
                   relax
l.11     \emph{Prepared By}
                           \\[0.1cm]
I wasn't able to read the size data for this font,
so I will ignore the font specification.
[Wizards can fix TFM files using TFtoPL/PLtoTF.]
You might try inserting a different font spec;
e.g., type `I\font<same font id>=<substitute font name>'.


Package microtype Warning: Font `TU/BasierCircle-Regular.otf(0)/m/it/10.95' does not specify its
(microtype)                \fontdimen 6 (width of an `em')! Therefore,
(microtype)                protrusion will not work with this font.

Missing character: There is no P in font nullfont!
Missing character: There is no r in font nullfont!
Missing character: There is no e in font nullfont!
Missing character: There is no p in font nullfont!
Missing character: There is no a in font nullfont!
Missing character: There is no r in font nullfont!
Missing character: There is no e in font nullfont!
Missing character: There is no d in font nullfont!
Missing character: There is no B in font nullfont!
Missing character: There is no y in font nullfont!
./cover.tex:16: Font TU/BasierCircle-Regular.otf(0)/m/n/8=[$path$/fonts/BasierCircle-Regular.otf]/OT at 8.0pt not loadable: Metric (TFM) file or installed font not found.
<to be read again>
                   relax
l.16     \end{tabular}}
                       \\[1cm]
I wasn't able to read the size data for this font,
so I will ignore the font specification.
[Wizards can fix TFM files using TFtoPL/PLtoTF.]
You might try inserting a different font spec;
e.g., type `I\font<same font id>=<substitute font name>'.


Package microtype Warning: Font `TU/BasierCircle-Regular.otf(0)/m/n/8' does not specify its
(microtype)                \fontdimen 6 (width of an `em')! Therefore,
(microtype)                protrusion will not work with this font.

./cover.tex:16: Font TU/BasierCircle-Regular.otf(0)/m/n/6=[$path$/fonts/BasierCircle-Regular.otf]/OT at 6.0pt not loadable: Metric (TFM) file or installed font not found.
<to be read again>
                   relax
l.16     \end{tabular}}
                       \\[1cm]
I wasn't able to read the size data for this font,
so I will ignore the font specification.
[Wizards can fix TFM files using TFtoPL/PLtoTF.]
You might try inserting a different font spec;
e.g., type `I\font<same font id>=<substitute font name>'.


Package microtype Warning: Font `TU/BasierCircle-Regular.otf(0)/m/n/6' does not specify its
(microtype)                \fontdimen 6 (width of an `em')! Therefore,
(microtype)                protrusion will not work with this font.

(/usr/local/texlive/2019/texmf-dist/tex/latex/microtype/mt-msa.cfg
File: mt-msa.cfg 2006/02/04 v1.1 microtype config. file: AMS symbols (a) (RS)
) (/usr/local/texlive/2019/texmf-dist/tex/latex/microtype/mt-msb.cfg
File: mt-msb.cfg 2005/06/01 v1.0 microtype config. file: AMS symbols (b) (RS)
) (/usr/local/texlive/2019/texmf-dist/tex/latex/microtype/mt-cmr.cfg
File: mt-cmr.cfg 2013/05/19 v2.2 microtype config. file: Computer Modern Roman (RS)
)
<<<<<<< Updated upstream
Missing character: There is no M in font nullfont!
Missing character: There is no u in font nullfont!
Missing character: There is no n in font nullfont!
Missing character: There is no e in font nullfont!
Missing character: There is no e in font nullfont!
Missing character: There is no f in font nullfont!
Missing character: There is no H in font nullfont!
Missing character: There is no a in font nullfont!
Missing character: There is no m in font nullfont!
Missing character: There is no e in font nullfont!
Missing character: There is no e in font nullfont!
Missing character: There is no d in font nullfont!
Missing character: There is no m in font nullfont!
Missing character: There is no u in font nullfont!
Missing character: There is no n in font nullfont!
Missing character: There is no e in font nullfont!
Missing character: There is no e in font nullfont!
Missing character: There is no f in font nullfont!
Missing character: There is no @ in font nullfont!
Missing character: There is no a in font nullfont!
Missing character: There is no u in font nullfont!
Missing character: There is no r in font nullfont!
Missing character: There is no u in font nullfont!
Missing character: There is no t in font nullfont!
Missing character: There is no . in font nullfont!
Missing character: There is no c in font nullfont!
Missing character: There is no o in font nullfont!
Missing character: There is no m in font nullfont!

./cover.tex:22: Font TU/BasierCircle-Regular.otf(0)/m/n/12=[$path$/fonts/BasierCircle-Regular.otf]/OT at 12.0pt not loadable: Metric (TFM) file or installed font not found.
<to be read again>
                   relax
l.22     {\large
                 \today}
=======
/Users/<USER>/Sites/Elixir/wraft-docs-api/lib/slugs/contract/template.tex:136: Font TU/Mallory-Book.otf(0)/m/n/8=[$path$/fonts/Mallory-Book.otf]/OT at 8.0pt not loadable: Metric (TFM) file or installed font not found.
<to be read again>
                   relax
l.136 $i
        f(path)$
I wasn't able to read the size data for this font,
so I will ignore the font specification.
[Wizards can fix TFM files using TFtoPL/PLtoTF.]
You might try inserting a different font spec;
e.g., type `I\font<same font id>=<substitute font name>'.


Package microtype Warning: Font `TU/Mallory-Book.otf(0)/m/n/8' does not specify its
(microtype)                \fontdimen 6 (width of an `em')! Therefore,
(microtype)                protrusion will not work with this font.

/Users/<USER>/Sites/Elixir/wraft-docs-api/lib/slugs/contract/template.tex:136: Font TU/Mallory-Book.otf(0)/m/n/6=[$path$/fonts/Mallory-Book.otf]/OT at 6.0pt not loadable: Metric (TFM) file or installed font not found.
<to be read again>
                   relax
l.136 $i
        f(path)$
>>>>>>> Stashed changes
I wasn't able to read the size data for this font,
so I will ignore the font specification.
[Wizards can fix TFM files using TFtoPL/PLtoTF.]
You might try inserting a different font spec;
e.g., type `I\font<same font id>=<substitute font name>'.
<<<<<<< Updated upstream

=======


Package microtype Warning: Font `TU/Mallory-Book.otf(0)/m/n/6' does not specify its
(microtype)                \fontdimen 6 (width of an `em')! Therefore,
(microtype)                protrusion will not work with this font.

(/usr/local/texlive/2019/texmf-dist/tex/latex/microtype/mt-msa.cfg
File: mt-msa.cfg 2006/02/04 v1.1 microtype config. file: AMS symbols (a) (RS)
) (/usr/local/texlive/2019/texmf-dist/tex/latex/microtype/mt-msb.cfg
File: mt-msb.cfg 2005/06/01 v1.0 microtype config. file: AMS symbols (b) (RS)
) (/usr/local/texlive/2019/texmf-dist/tex/latex/microtype/mt-cmr.cfg
File: mt-cmr.cfg 2013/05/19 v2.2 microtype config. file: Computer Modern Roman (RS)
)

! LaTeX Error: File `$path$/cover.tex' not found.
>>>>>>> Stashed changes

Package microtype Warning: Font `TU/BasierCircle-Regular.otf(0)/m/n/12' does not specify its
(microtype)                \fontdimen 6 (width of an `em')! Therefore,
(microtype)                protrusion will not work with this font.

<<<<<<< Updated upstream
Missing character: There is no A in font nullfont!
Missing character: There is no p in font nullfont!
Missing character: There is no r in font nullfont!
Missing character: There is no i in font nullfont!
Missing character: There is no l in font nullfont!
Missing character: There is no 8 in font nullfont!
Missing character: There is no , in font nullfont!
Missing character: There is no 2 in font nullfont!
Missing character: There is no 0 in font nullfont!
Missing character: There is no 2 in font nullfont!
Missing character: There is no 0 in font nullfont!
[1])
/Users/<USER>/Sites/Elixir/wraft-docs-api/lib/slugs/contract/template.tex:131: Font TU/BasierCircle-Regular.otf(0)/m/n/14.4=[$path$/fonts/BasierCircle-Regular.otf]/OT at 14.4pt not loadable: Metric (TFM) file or installed font not found.
<to be read again>
                   relax
l.131 \tableofcontents

I wasn't able to read the size data for this font,
so I will ignore the font specification.
[Wizards can fix TFM files using TFtoPL/PLtoTF.]
You might try inserting a different font spec;
e.g., type `I\font<same font id>=<substitute font name>'.
=======
Enter file name:
/Users/<USER>/Sites/Elixir/wraft-docs-api/lib/slugs/contract/template.tex:137: Emergency stop.
<read *>

l.137 \input{$path$/cover}
                          ^^M
*** (cannot \read from terminal in nonstop modes)
>>>>>>> Stashed changes


Package microtype Warning: Font `TU/BasierCircle-Regular.otf(0)/m/n/14.4' does not specify its
(microtype)                \fontdimen 6 (width of an `em')! Therefore,
(microtype)                protrusion will not work with this font.

/Users/<USER>/Sites/Elixir/wraft-docs-api/lib/slugs/contract/template.tex:131: Font TU/BasierCircle-Regular.otf(0)/bx/n/14.4=[$path$/fonts/BasierCircle-Medium.otf]/OT at 14.4pt not loadable: Metric (TFM) file or installed font not found.
<to be read again>
                   relax
l.131 \tableofcontents

I wasn't able to read the size data for this font,
so I will ignore the font specification.
[Wizards can fix TFM files using TFtoPL/PLtoTF.]
You might try inserting a different font spec;
e.g., type `I\font<same font id>=<substitute font name>'.


Package microtype Warning: Font `TU/BasierCircle-Regular.otf(0)/bx/n/14.4' does not specify its
(microtype)                \fontdimen 6 (width of an `em')! Therefore,
(microtype)                protrusion will not work with this font.

Missing character: There is no T in font nullfont!
Missing character: There is no a in font nullfont!
Missing character: There is no b in font nullfont!
Missing character: There is no l in font nullfont!
Missing character: There is no e in font nullfont!
Missing character: There is no o in font nullfont!
Missing character: There is no f in font nullfont!
Missing character: There is no C in font nullfont!
Missing character: There is no o in font nullfont!
Missing character: There is no n in font nullfont!
Missing character: There is no t in font nullfont!
Missing character: There is no e in font nullfont!
Missing character: There is no n in font nullfont!
Missing character: There is no t in font nullfont!
Missing character: There is no s in font nullfont!
\tf@toc=\write5
\openout5 = `template.toc'.

Missing character: There is no 1 in font nullfont!
[1]
Package atveryend Info: Empty hook `BeforeClearDocument' on input line 135.
Package atveryend Info: Empty hook `AfterLastShipout' on input line 135.
 (./template.aux)
Package atveryend Info: Empty hook `AtVeryEndDocument' on input line 135.
Package atveryend Info: Executing hook `AtEndAfterFileList' on input line 135.


Package rerunfilecheck Warning: File `template.out' has changed.
(rerunfilecheck)                Rerun to get outlines right
(rerunfilecheck)                or use package `bookmark'.

Package rerunfilecheck Info: Checksums for `template.out':
(rerunfilecheck)             Before:
(rerunfilecheck)             After:  D41D8CD98F00B204E9800998ECF8427E.
 )
(\end occurred inside a group at level 1)

### simple group (level 1) entered at line 119 ({)
### bottom level
Here is how much of TeX's memory you used:
<<<<<<< Updated upstream
 39425 strings out of 492916
 772579 string characters out of 6133344
 896395 words of memory out of 5000000
 42950 multiletter control sequences out of 15000+600000
 540466 words of font info for 48 fonts, out of 8000000 for 9000
 1348 hyphenation exceptions out of 8191
 62i,8n,92p,873b,1232s stack positions out of 5000i,500n,10000p,200000b,80000s

Output written on template.pdf (3 pages).
=======
 39379 strings out of 492916
 770849 string characters out of 6133344
 893716 words of memory out of 5000000
 42916 multiletter control sequences out of 15000+600000
 540466 words of font info for 48 fonts, out of 8000000 for 9000
 1348 hyphenation exceptions out of 8191
 62i,3n,92p,873b,1013s stack positions out of 5000i,500n,10000p,200000b,80000s
No pages of output.
>>>>>>> Stashed changes
