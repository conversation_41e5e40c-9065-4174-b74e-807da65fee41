<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/picnic" />
    <title>PoetBin</title>
    <style>
      .container {
        width: 50ch;
        margin: auto;
        padding-top: 10vh;
      }
      h1 {
        margin-bottom: 0;
      }
      body {
        color: #111;
        font-size: 1rem;
        line-height: 1.5;
        background: #000;
        color: #fff;
        font-family: "courier";
      }
      p {
        color: greenyellow;
      }
      button {
        background-color: greenyellow;
        color: #000;
      }
      .othukkappettavan {
        min-width: 30%;
      }
    </style>
  </head>
  <body>
    <div class="container">
      <div class="othukkappettavan">
        <svg
          width="24"
          height="28"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <defs>
            <linearGradient
              id="fun-gradient"
              x1="50%"
              y1="0%"
              x2="50%"
              y2="100%"
            >
              <stop offset="0%" stop-color="#e9ecef">
                <animate
                  attributeName="stop-color"
                  values="#e9ecef; #495057; #e9ecef"
                  dur="3s"
                  repeatCount="indefinite"
                ></animate>
              </stop>
              <stop offset="100%" stop-color="#495057">
                <animate
                  attributeName="stop-color"
                  values="#e9ecef; #495057; #e9ecef"
                  dur="3s"
                  repeatCount="indefinite"
                ></animate>
              </stop>
            </linearGradient>
          </defs>
          <path
            fill-rule="evenodd"
            clip-rule="evenodd"
            d="M9.11 9.06V1.65S11.87.6 14.16 0v12.15H0v-.21l2-2.88h7.11Zm5.07 9v7.41s-2.76 1.05-5.04 1.66V14.97H23.3v.22l-2 2.87h-7.12Z"
            fill="url('#fun-gradient')"
          ></path>
        </svg>
      </div>
      <h1>Wraft</h1>
      <p>> Wraft is an open-source document lifecycle management software</p>
    </div>
  </body>
</html>
