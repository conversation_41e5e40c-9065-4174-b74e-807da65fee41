defmodule WraftDocWeb.Api.V1.FormEntryView do
  use <PERSON>raft<PERSON>oc<PERSON>eb, :view

  alias __MODULE__

  def render("form_entry.json", %{form_entry: form_entry}) do
    %{
      id: form_entry.id,
      form_id: form_entry.form_id,
      user_id: form_entry.user_id,
      data: form_entry.data,
      status: form_entry.status,
      inserted_at: form_entry.inserted_at,
      updated_at: form_entry.updated_at
    }
  end

  def render("index.json", %{
        form_entries: form_entries,
        page_number: page_number,
        total_pages: total_pages,
        total_entries: total_entries
      }) do
    %{
      entries: render_many(form_entries, FormEntryView, "form_entry.json", as: :form_entry),
      page_number: page_number,
      total_pages: total_pages,
      total_entries: total_entries
    }
  end
end
