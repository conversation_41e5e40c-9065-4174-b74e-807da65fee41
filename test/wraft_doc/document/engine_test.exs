defmodule WraftDoc.Documents.EngineTest do
  use WraftDoc.ModelCase
  alias WraftDoc.Documents.Engine
  @moduletag :document

  @valid_attrs %{
    name: "engine-1",
    api_route: "localhost:4000/api/route"
  }
  @invalid_attrs %{}

  # TODO include tests for unique constraint
  test "changeset with valid attrs" do
    changeset = Engine.changeset(%Engine{}, @valid_attrs)
    assert changeset.valid?
  end

  test "changeset with invalid attrs" do
    changeset = Engine.changeset(%Engine{}, @invalid_attrs)
    refute changeset.valid?
  end
end
