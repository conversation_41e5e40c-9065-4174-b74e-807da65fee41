import Config

# For development, we disable any cache and enable
# debugging and code reloading.
#
# The watchers configuration can be used to run external
# watchers to your application. For example, we use it
# with esbuild to bundle .js and .css sources.
config :wraft_doc, WraftDocWeb.Endpoint,
  http: [ip: {0, 0, 0, 0}, port: System.get_env("PORT") || 4000],
  debug_errors: true,
  code_reloader: true,
  check_origin: false,
  secret_key_base: Map.fetch!(System.get_env(), "SECRET_KEY_BASE"),
  watchers: [
    # Start the esbuild watcher by calling Esbuild.install_and_run(:default, args)
    esbuild: {Esbuild, :install_and_run, [:default, ~w(--sourcemap=inline --watch)]}
  ]

# ## SSL Support
#
# In order to use HTTPS in development, a self-signed
# certificate can be generated by running the following
# command from your terminal:
#
#     openssl req -new -newkey rsa:4096 -days 365 -nodes -x509 -subj "/C=US/ST=Denial/L=Springfield/O=Dis/CN=www.example.com" -keyout priv/server.key -out priv/server.pem
#
# The `http:` config above can be replaced with:
#
#     https: [port: 4000, keyfile: "priv/server.key", certfile: "priv/server.pem"],
#
# If desired, both `http:` and `https:` keys can be
# configured to run both http and https servers on
# different ports.

# Watch static and templates for browser reloading.
config :wraft_doc, WraftDocWeb.Endpoint,
  live_reload: [
    patterns: [
      ~r{priv/static/.*(js|css|png|jpeg|jpg|gif|svg)$},
      ~r{priv/gettext/.*(po)$},
      ~r{lib/wraft_doc_web/views/.*(ex)$},
      ~r{lib/wraft_doc_web/templates/.*(eex)$}
    ]
  ]

# Do not include metadata nor timestamps in development logs
config :logger, :console, format: "[$level] $message\n"

# Set a higher stacktrace during development. Avoid configuring such
# in production as building large stacktraces may be expensive.
config :phoenix, :stacktrace_depth, 20

config :wraft_doc, WraftDoc.Repo,
  adapter: Ecto.Adapters.Postgres,
  username: System.get_env("DEV_DB_USERNAME") || "postgres",
  password: System.get_env("DEV_DB_PASSWORD") || "postgres",
  database: System.get_env("DEV_DB_NAME") || "wraft_doc_dev",
  hostname: System.get_env("DEV_DB_HOST") || "localhost",
  port: (System.get_env("DEV_DB_PORT") || "5432") |> String.to_integer(),
  pool_size: 10,
  timeout: 300_000,
  show_sensitive_data_on_connetion_error: true
