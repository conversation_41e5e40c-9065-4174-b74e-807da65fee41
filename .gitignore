# The directory Mix will write compiled artifacts to.
/_build
.elixir_ls
/db
/pg

# Ignore the environment variables file.
.env
.env.dev
.env.prod
.env.stage
.env.test
.env.local

# The directory Mix downloads your dependencies sources to.
/deps

# Also ignore archive artifacts (built via "mix archive.build").
*.ez

/uploads
/temp
/users
/organisations
/priv/static/swagger.json
/typesense-data/

# Rust build artifacts
/native/*/target/
/priv/native/*.so

# Ignore assets that are produced by build tools.
/priv/static/assets/

# Ignore digested assets cache.
/priv/static/cache_manifest.json

# Where 3rd-party dependencies like ExDoc output generated docs.
/doc/

# Ignore .fetch files in case you like to edit your project deps locally.
/.fetch

# Ignore .DS_Store files on macOS.
**/.DS_Store

# If you run "mix test --cover", coverage assets end up here.
/cover

# If the VM crashes, it generates a dump, let's ignore it too.
erl_crash.dump

# Files matching config/*.secret.exs pattern contain sensitive
# data and you should not commit them into version control.
#
# Alternatively, you may comment the line below and commit the
# secrets files as long as you replace their contents by environment
# variables.
# /config/*.secret.exs
# In case you use Node.js/npm, you want to ignore these.
npm-debug.log
/assets/node_modules/
.idea/
/lib/slugs/**/template.aux
/lib/slugs/**/template.fdb_latexmk
/lib/slugs/**/template.fls
/lib/slugs/**/template.log
priv/slugs/organisation
!priv/slugs/contract/**
!priv/slugs/gantt_chart/**
!priv/slugs/pletter/**
.env.custom
