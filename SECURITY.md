# Wraft Security Policy

## Commitment to Security

At Wraft, we are committed to ensuring the security of our products and services. We value the contributions of security researchers worldwide and encourage responsible disclosure of potential vulnerabilities.

## Reporting a Vulnerability

If you believe you have discovered a security issue in any of <PERSON><PERSON>'s products or services, we urge you to report it to us immediately. You can do so by:

Emailing <NAME_EMAIL>

We appreciate your efforts in helping us maintain a secure environment for our users.

## Responsible Disclosure Guidelines

We kindly request that you:

1. Notify us promptly upon discovery of a potential security issue.
2. Provide us with a reasonable timeframe to address the issue before any public disclosure.
3. Make every effort to avoid privacy violations, data destruction, and service interruptions during your research.
4. Only interact with accounts you own or have explicit permission to access.

## Scope

Our security program covers:

- All current releases of <PERSON><PERSON>'s services
- Official product downloads available at https://wraft.co
- Source code hosted at https://github.com/wraft.co

## Out of Scope

The following are considered out of scope for our security program:

- Previously reported issues or known vulnerabilities
- Issues in upstream dependencies that have been reported to their respective maintainers
- Attacks requiring physical access to a user's device
- Self-XSS
- Vulnerabilities in outdated versions of Wraft
- Missing security best practices that do not directly lead to a vulnerability
- Issues with no impact on the general public
- Vulnerabilities in third-party software or protocols not under <PERSON><PERSON>'s control

## Prohibited Activities

While conducting your research, please refrain from:

- Launching denial of service attacks
- Spamming
- Social engineering (including phishing) of Wraft staff or contractors
- Any physical attempts against Wraft property or data centers

## Disclosure Policy

We are committed to addressing security issues promptly and transparently. Our policy includes:

1. Acknowledging receipt of your vulnerability report in a timely manner.
2. Providing regular updates on the progress of addressing the reported issue.
3. Notifying you when the vulnerability has been fixed.
4. Publicly disclosing the issue after it has been resolved, giving credit to the reporter (unless anonymity is requested).

## Recognition

We value the efforts of security researchers and, when appropriate, we may offer:

- Public acknowledgment of your contribution (with your consent)
- Inclusion in our security hall of fame

## Legal Safe Harbor

We will not pursue legal action against security researchers who:

1. Make a good faith effort to comply with this policy
2. Do not engage in any prohibited activities outlined above

We thank you for your dedication to improving the security of Wraft and protecting our users. Your contributions are invaluable to our ongoing commitment to security excellence.
