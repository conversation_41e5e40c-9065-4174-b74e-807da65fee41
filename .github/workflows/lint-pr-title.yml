name: "lint-pr-title"

on:
  pull_request_target:
    types:
      - opened
      - edited
      - synchronize

jobs:
  lint-pr-title:
    runs-on: ubuntu-latest
    steps:
      - name: Install commitlint
        run: npm install -g @commitlint/config-conventional@19 @commitlint/cli@19

      - name: Configure commitlint
        run: |
          echo "export default { extends: ['@commitlint/config-conventional'] };" > commitlint.config.mjs

      - name: Check PR Title
        run: |
          echo The Title of your PR is "$PR_TITLE"
          echo "$PR_TITLE" | commitlint --verbose
        env:
          PR_TITLE: ${{ github.event.pull_request.title }}
