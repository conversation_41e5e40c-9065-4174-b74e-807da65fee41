version: "3.8"
services:
  frontend:
    image: quay.io/wraft/wraft-frontend:latest
    ports:
      - "3200:3000"

  backend:
    stdin_open: true
    build:
      context: .
      dockerfile: Dockerfile.dev
      args:
        SECRET_KEY_BASE: ${SECRET_KEY_BASE}
        DATABASE_URL: ${DATABASE_URL}
        SELF_HOSTED: ${SELF_HOSTED}
    environment:
      MIX_ENV: dev
      SECRET_KEY_BASE: ${SECRET_KEY_BASE}
      GUARDIAN_KEY: ${GUARDIAN_KEY}
      DEV_DB_USERNAME: ${DEV_DB_USERNAME}
      DEV_DB_PASSWORD: ${DEV_DB_PASSWORD}
      DEV_DB_NAME: ${DEV_DB_NAME}
      DEV_DB_HOST: db
      DEV_DB_PORT: 5433
      MINIO_URL: http://127.0.0.1:9000
      MINIO_HOST: minio
      MINIO_PORT: 9000
      MINIO_BUCKET: ${MINIO_BUCKET}
      MINIO_ROOT_USER: ${MINIO_ROOT_USER}
      MINIO_ROOT_PASSWORD: ${MINIO_ROOT_PASSWORD}
      SENTRY_DSN: ${SENTRY_DSN}
      XELATEX_PATH: ${XELATEX_PATH}
      TYPESENSE_API_KEY: ${TYPESENSE_API_KEY}
      TYPESENSE_HOST: typesense
      SELF_HOSTED: ${SELF_HOSTED}
      PADDLE_API_KEY: ${PADDLE_API_KEY}
      PADDLE_WEBHOOK_SECRET_KEY: ${PADDLE_WEBHOOK_SECRET_KEY}
      PADDLE_BASE_URL: ${PADDLE_BASE_URL}
    ports:
      - 4000:4000
    depends_on:
      - db
      - minio
      - typesense
    restart: always

  db:
    image: postgres:14-alpine
    environment:
      POSTGRES_USER: ${DEV_DB_USERNAME}
      POSTGRES_PASSWORD: ${DEV_DB_PASSWORD}
      POSTGRES_DB: ${DEV_DB_NAME}
      PGPORT: 5433
    restart: always
    ports:
      - 5433:5433
    volumes:
      - db:/var/lib/postgresql/data

  minio:
    image: quay.io/minio/minio
    ports:
      - 9000:9000
      - 9001:9001
    environment:
      MINIO_ROOT_USER: ${MINIO_ROOT_USER}
      MINIO_ROOT_PASSWORD: ${MINIO_ROOT_PASSWORD}
      MINIO_SERVER_URL: http://127.0.0.1:9000
    volumes:
      - miniodata:/data:rw
    command: minio server /data --console-address ":9001" --address :9000

  createbucket:
    image: minio/mc
    restart: on-failure:5
    environment:
      MINIO_URL: http://minio:9000
      MINIO_BUCKET: ${MINIO_BUCKET}
      MINIO_ROOT_USER: ${MINIO_ROOT_USER}
      MINIO_ROOT_PASSWORD: ${MINIO_ROOT_PASSWORD}
    depends_on:
      - minio
    entrypoint: >
      /bin/sh -c "
      /usr/bin/mc config host add myminio $$MINIO_URL $${MINIO_ROOT_USER} $$MINIO_ROOT_PASSWORD;
      /usr/bin/mc mb -p myminio/$${MINIO_BUCKET};
      /usr/bin/mc policy set public myminio/$${MINIO_BUCKET};
      exit 0;"

  typesense:
    image: typesense/typesense:27.1
    ports:
      - "8108:8108"
    environment:
      TYPESENSE_API_KEY: ${TYPESENSE_API_KEY}
    volumes:
      - ./typesense-data:/data
    command: >
      --data-dir /data
      --api-key=${TYPESENSE_API_KEY}

volumes:
  db:
  miniodata:
