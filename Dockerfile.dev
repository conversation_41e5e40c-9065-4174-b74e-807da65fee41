FROM hexpm/elixir:1.15.8-erlang-25.2.3-debian-bookworm-20250520

# Install dependencies
RUN apt-get update && apt-get install -y --fix-missing \
    postgresql-client \
    inotify-tools \
    build-essential \
    xorg \
    libssl-dev \
    libxrender-dev \
    git \
    wget \
    gdebi \
    xvfb \
    wkhtmltopdf \
    texlive-fonts-recommended \
    texlive-plain-generic \
    texlive-latex-extra \
    texlive-xetex \
    curl \
    imagemagick \
    ca-certificates \
    openjdk-17-jdk && \
    update-ca-certificates && \
    rm -rf /var/lib/apt/lists/*

# Install Rust toolchain for Rustler
RUN curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh -s -- -y
ENV PATH="/root/.cargo/bin:${PATH}"

# Set Java environment
ENV JAVA_HOME=/usr/lib/jvm/java-17-openjdk-amd64
ENV PATH="$JAVA_HOME/bin:$PATH"

# Install Pandoc
RUN ARCH=$(dpkg --print-architecture) && \
    case "$ARCH" in \
        amd64) PANDOC_DEB=pandoc-3.6.3-1-amd64.deb ;; \
        arm64) PANDOC_DEB=pandoc-3.6.3-1-arm64.deb ;; \
        *) echo "Unsupported architecture: $ARCH" && exit 1 ;; \
    esac && \
    wget -q https://github.com/jgm/pandoc/releases/download/3.6.3/${PANDOC_DEB} && \
    dpkg -i ${PANDOC_DEB} && \
    rm -f ${PANDOC_DEB}

# Install Typst
RUN wget -q https://github.com/typst/typst/releases/download/v0.13.0/typst-x86_64-unknown-linux-musl.tar.xz && \
    tar -xf typst-x86_64-unknown-linux-musl.tar.xz && \
    mv typst-x86_64-unknown-linux-musl/typst /usr/local/bin/typst && \
    rm -rf typst-x86_64-unknown-linux-musl typst-x86_64-unknown-linux-musl.tar.xz

# Create a wrapper for wkhtmltopdf to run in headless mode
RUN echo "xvfb-run -a -s \"-screen 0 640x480x16\" wkhtmltopdf \"\$@\"" > /usr/local/bin/wkhtmltopdf-wrapper && \
    chmod +x /usr/local/bin/wkhtmltopdf-wrapper


WORKDIR /app
COPY config /app/config
COPY lib /app/lib
COPY native /app/native
COPY mix.exs mix.lock /app/
COPY priv priv
COPY assets assets

ARG SECRET_KEY_BASE
ARG SELF_HOSTED

# only for dev
COPY entrypoint_dev.sh /app/
RUN chmod +x /app/entrypoint_dev.sh

RUN mix local.hex --force && \
    mix local.rebar --force && \
    mix deps.get

RUN mix do compile

EXPOSE 4000
CMD ["bash", "/app/entrypoint_dev.sh"]
